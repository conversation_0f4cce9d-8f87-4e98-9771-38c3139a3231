import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { AdPlatforms, IGroweasyUser, IPartnerConfig } from 'src/types';
import MobileContainer from '@/components/lib/MobileContainer';
import { GROWEASY_CAMPAIGN_TYPE, ICampaign } from 'src/types/campaigns';
import {
  QueryParams,
  OnboardingStepIds,
  ONBOARDING_STEPS,
  GROWEASY_DEFAULT_PAGE_ID,
  GLOBALS,
} from 'src/constants';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import BackIcon from '@/images/common/back-arrow.svg';
import classNames from 'classnames';
import { showToastMessage } from 'src/modules/toast';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import { useMutation, useQuery } from 'react-query';
import {
  createCustomConversionAction,
  createOrUpdateCampaign,
  generateLeadgenForm,
  getBusinessCategories,
  getCampaignDetails,
  populateAdCopies,
  populateDetailedTargeting,
  populateGoogleLeadFormContent,
  populateGoogleSearchKeywordsSuggestions,
  populateSpecialAdCategories,
} from 'src/actions/onboarding';
import { getCommonHeaders } from 'src/actions';
import BusinessDetailsComp from '@/components/onboarding/business_details/BusinessDetailsComp';
import TargetAudienceComp from '@/components/onboarding/target_audience/TargetAudienceComp';
import AdBannersComp from '@/components/onboarding/ad_banners/AdBannersCompV2';
import AdPreviewContainer from '@/components/onboarding/ad_preview/AdPreviewContainer';
import AdBudgetContainer from '@/components/onboarding/ad_budget/AdBudgetContainer';
import ReviewAndLaunchCampaign from '@/components/onboarding/review_and_launch/ReviewAndLaunchCampaign';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import DetailedTargetingContainer from '@/components/onboarding/detailed_targeting/DetailedTargetingContainer';
import FbPageContainer from '@/components/onboarding/fb_page/FbPageContainer';
import AdVideosComp from '@/components/onboarding/ad_videos/AdVideosComp';
import LeadFormComp from '@/components/onboarding/lead_form/LeadFormComp';
import GoogleKeywordsComp from '@/components/onboarding/google_keywords/GoogleKeywordsComp';
import AdLanguageComp from '@/components/onboarding/ad_language/AdLanguageComp';
import ProductUspsComp from '@/components/onboarding/product_usps/ProductUspsComp';
import { getUserProfile } from 'src/actions/profile';

interface IOnboardingProps {
  user?: IGroweasyUser;
  partnerConfig?: IPartnerConfig;
}

const Onboarding = (props: IOnboardingProps) => {
  const { user, partnerConfig } = props;

  const [campaignDetails, setCampaignDetails] = useState<ICampaign>({});
  const [selectedStepId, setSelectedStepId] = useState(
    OnboardingStepIds.BUSINESS_DETAILS,
  );
  const onboardingStepRefs = useRef<HTMLDivElement[]>([]);

  const router = useRouter();
  const adPlatform =
    campaignDetails?.platform ??
    router.query[QueryParams.PLATFORM] ??
    AdPlatforms.META;
  const campaignType =
    campaignDetails?.type ??
    (router.query[QueryParams.TYPE] as GROWEASY_CAMPAIGN_TYPE) ??
    GROWEASY_CAMPAIGN_TYPE.LEAD_FORM;

  const campaignId = (router.query[QueryParams.CAMPAIGN_ID] ??
    campaignDetails?.id) as string;

  const campaignDetailsResponse = useQuery(
    ['getCampaignDetails', campaignId],
    () => {
      return getCampaignDetails({
        headers: getCommonHeaders(user),
        queryParams: {
          ...router.query,
          [QueryParams.CAMPAIGN_ID]: campaignId,
        },
      });
    },
    {
      retry: 0,
      enabled: !!campaignId,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'onboarding.getCampaignDetails');
      },
      onSuccess: (response) => {
        setCampaignDetails(response.data);
      },
    },
  );

  const businessCategoriesResponse = useQuery(
    'getBusinessCategories',
    () => {
      return getBusinessCategories({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 2,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'onboarding.getBusinessCategories',
        );
      },
    },
  );

  useQuery(
    'getUserProfile',
    () => {
      return getUserProfile({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      enabled: !!user?.authToken,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'onboarding.getUserProfile');
      },
      onSuccess: (response) => {
        if (response?.data) {
          GLOBALS.userProfile = response.data;
        }
      },
    },
  );

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  useEffect(() => {
    // Scroll the selected step into view when the selectedStepId changes
    const selectedStepIndex = getFilteredOnboardingSteps().findIndex(
      (item) => item.id === selectedStepId,
    );
    if (
      selectedStepIndex !== -1 &&
      onboardingStepRefs.current[selectedStepIndex]
    ) {
      onboardingStepRefs.current[selectedStepIndex]?.scrollIntoView({
        behavior: 'smooth',
        inline: 'center', // Centers the item in the viewport
      });
    }
  }, [selectedStepId]);

  const createOrUpdateCampaignMutation = useMutation(createOrUpdateCampaign);

  const onBackPressed = () => {
    router.back();
  };

  const getFilteredOnboardingSteps = () => {
    return ONBOARDING_STEPS.filter((item) => {
      // hide FB Page for Google campaigns
      if (
        [OnboardingStepIds.FACEBOOK_PAGE].includes(item.id) &&
        adPlatform === AdPlatforms.GOOGLE
      ) {
        return false;
      }
      // hide Ad Banners, Videos & Language for Google Search
      if (
        [
          OnboardingStepIds.AD_BANNERS,
          OnboardingStepIds.AD_VIDEOS,
          OnboardingStepIds.AD_LANGUAGE,
        ].includes(item.id) &&
        [
          GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
          GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
        ].includes(campaignType)
      ) {
        return false;
      }
      if (
        item.id === OnboardingStepIds.LEAD_FORM &&
        [
          GROWEASY_CAMPAIGN_TYPE.CTWA,
          GROWEASY_CAMPAIGN_TYPE.META_SALES,
          GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX,
          GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
        ].includes(campaignType as GROWEASY_CAMPAIGN_TYPE)
      ) {
        return false;
      }
      if (
        [OnboardingStepIds.GOOGLE_KEYWORDS].includes(item.id) &&
        adPlatform === AdPlatforms.META
      ) {
        return false;
      }
      return true;
    });
  };

  const onOnboardingStepTabClick = (stepId: OnboardingStepIds) => {
    let allowed = false;
    switch (stepId) {
      case OnboardingStepIds.BUSINESS_DETAILS: {
        allowed = true;
        break;
      }
      // Business Details is must for enabling Audience tab
      case OnboardingStepIds.AUDIENCE: {
        if (
          campaignDetails?.details?.business_details?.business_category &&
          campaignDetails?.details?.business_details
            ?.product_or_service_description
        ) {
          allowed = true;
        }
        break;
      }
      // Audience Details is must for enabling USPs
      case OnboardingStepIds.PRODUCT_USPS: {
        if (
          campaignDetails?.details?.targeting?.geo_locations &&
          campaignDetails?.details?.targeting?.age_min &&
          campaignDetails?.details?.targeting?.age_max
        ) {
          allowed = true;
        }
        break;
      }
      case OnboardingStepIds.GOOGLE_KEYWORDS:
      case OnboardingStepIds.AD_LANGUAGE:
      case OnboardingStepIds.AD_BANNERS:
      case OnboardingStepIds.AD_COPIES: {
        // USPs is must for enabling Language/Keywords/Banners/Copies tabs
        if (campaignDetails?.details?.ai_assisted_product_usps?.length) {
          allowed = true;
        }
        // keywords is must for Banners/Copies
        if (
          [OnboardingStepIds.AD_BANNERS, OnboardingStepIds.AD_COPIES].includes(
            stepId,
          ) &&
          campaignDetails?.platform === AdPlatforms.GOOGLE &&
          !campaignDetails?.google_ads_data?.search_keywords?.length
        ) {
          allowed = false;
        }
        break;
      }
      case OnboardingStepIds.AD_PREVIEW:
      case OnboardingStepIds.DETAILED_TARGETING:
      case OnboardingStepIds.AD_VIDEOS:
      case OnboardingStepIds.AD_BUDGET: {
        // for Google search, keywords are must
        if (
          [
            GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH,
            GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL,
          ].includes(campaignType) &&
          campaignDetails?.google_ads_data?.search_keywords?.length
        ) {
          allowed = true;
        }
        // Banners & Copies are must for enabling Preview/Budget tab,
        if (
          campaignDetails?.details?.ad_banners?.length &&
          campaignDetails?.details?.ad_copies?.length
        ) {
          allowed = true;
        }
        break;
      }
      case OnboardingStepIds.FACEBOOK_PAGE:
      case OnboardingStepIds.LEAD_FORM:
      case OnboardingStepIds.REVIEW_AND_LAUNCH: {
        // for CTWA, custom Fb selection is must
        if (
          stepId === OnboardingStepIds.REVIEW_AND_LAUNCH &&
          campaignType === GROWEASY_CAMPAIGN_TYPE.CTWA
        ) {
          const fbPageId = campaignDetails?.details?.config?.fb_page_id;
          if (!fbPageId || fbPageId === GROWEASY_DEFAULT_PAGE_ID) {
            break;
          }
        }
        // for G Search, custom form link is must
        if (
          stepId === OnboardingStepIds.REVIEW_AND_LAUNCH &&
          campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH
        ) {
          if (!campaignDetails?.google_ads_data?.lead_form_url) {
            break;
          }
        }
        // Budget is must
        if (
          campaignDetails?.details?.budget_and_scheduling?.currency &&
          campaignDetails?.details?.budget_and_scheduling?.end_time
        ) {
          allowed = true;
        }
        break;
      }
    }
    if (allowed) {
      setSelectedStepId(stepId);
    } else {
      showToastMessage('Please fill in all the details to Unlock.');
    }
  };

  const populateSpecialAdCategoriesUsingAi = (campaignDetails: ICampaign) => {
    if (campaignDetails?.details?.business_details && campaignDetails.id) {
      const params = {
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
        },
      };
      populateSpecialAdCategories(params).catch(() => {});
    }
  };

  const populateSearchKeywordsSuggestionsUsingAi = (
    campaignDetails: ICampaign,
  ) => {
    if (
      campaignDetails?.details?.business_details &&
      campaignDetails.id &&
      campaignDetails.platform === AdPlatforms.GOOGLE
    ) {
      const params = {
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
        },
      };
      populateGoogleSearchKeywordsSuggestions(params)
        .then(() => {
          // refresh campaign
          void campaignDetailsResponse.refetch();
        })
        .catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'onboarding.populateSearchKeywordsSuggestionsUsingAi',
          );
        });
    }
  };

  const populateCampaignDataUsingAi = (campaignDetails: ICampaign) => {
    if (
      ((campaignDetails?.details?.targeting?.geo_locations &&
        Object.keys(campaignDetails?.details?.targeting?.geo_locations).length >
          1) ||
        campaignDetails?.google_ads_data?.geo_locations?.length > 0) &&
      campaignDetails?.details?.ai_assisted_product_usps?.length &&
      campaignDetails.id
    ) {
      const params = {
        headers: getCommonHeaders(user),
        queryParams: {
          [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
        },
      };
      // do not care about response, recheck is there on last step of onboarding
      // sequential call so that there is no conflict in updation
      // for parallel calls, we'll have to store all campaign.details in separate collection
      generateLeadgenForm(params)
        .catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'populateCampaignDataUsingAi.generateLeadgenForm',
          );
        })
        .finally(() => {
          populateAdCopies(params, campaignType)
            .catch((error: Error) => {
              logApiErrorAndShowToastMessage(
                error,
                'populateCampaignDataUsingAi.populateAdCopies',
              );
            })
            .finally(() => {
              // refresh campaign
              void campaignDetailsResponse.refetch();
            });
          /*
          Since targeting is not being used and it can cause below error, not populating it
          */
          /*populateDetailedTargeting(params)
            .catch((error: Error) => {
              logApiErrorAndShowToastMessage(
                error,
                'populateCampaignDataUsingAi.populateDetailedTargeting',
              );
            })
            .finally(() => {
              populateAdCopies(params)
                .catch((error: Error) => {
                  logApiErrorAndShowToastMessage(
                    error,
                    'populateCampaignDataUsingAi.populateAdCopies',
                  );
                })
                .finally(() => {
                  // refresh campaign
                  void campaignDetailsResponse.refetch();
                });
            });*/
        });
      if (campaignDetails?.platform === AdPlatforms.META) {
        populateDetailedTargeting(params).catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'populateCampaignDataUsingAi.populateDetailedTargeting',
          );
        });
      }
      if (campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_CALL) {
        populateGoogleLeadFormContent(params).catch((error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'populateCampaignDataUsingAi.populateGoogleLeadFormContent',
          );
        });
      }
    }
  };

  // only for Google P-Max and custom landing page in Google Search
  const triggerCustomConversionActionCreation = (
    campaignDetails: ICampaign,
    triggerRefresh: boolean,
  ) => {
    const params = {
      headers: getCommonHeaders(user),
      queryParams: {
        [QueryParams.CAMPAIGN_ID]: campaignDetails.id,
      },
    };
    createCustomConversionAction(params)
      .then(() => {
        // refresh campaign
        if (triggerRefresh) {
          void campaignDetailsResponse.refetch();
        }
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'onboarding.triggerCustomConversionActionCreation',
        );
      });
  };

  const moveToNextStep = () => {
    const onboardingSteps = getFilteredOnboardingSteps();
    for (let i = 0; i < onboardingSteps.length; i++) {
      if (onboardingSteps[i].id === selectedStepId) {
        // select next step
        if (onboardingSteps[i + 1]?.id) {
          setSelectedStepId(onboardingSteps[i + 1].id);
        }
        break;
      }
    }
  };

  const saveCampaignDetails = (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => {
    createOrUpdateCampaignMutation
      .mutateAsync({
        queryParams: router.query as Record<string, string>,
        headers: getCommonHeaders(user),
        data,
      })
      .then((response) => {
        // update campaign_id in url in case of creation but do not reload
        if (!router.query[QueryParams.CAMPAIGN_ID]) {
          logEvent(EVENT_NAMES.campaign_created);
          void router.push(
            {
              pathname: router.pathname,
              query: {
                ...router.query,
                [QueryParams.CAMPAIGN_ID]: response.data?.id,
              },
            },
            undefined,
            {
              shallow: true,
            },
          );
        }

        setCampaignDetails(response.data);

        const moveToNextStepOnSuccess = [
          OnboardingStepIds.BUSINESS_DETAILS,
          OnboardingStepIds.PRODUCT_USPS,
          OnboardingStepIds.AUDIENCE,
          OnboardingStepIds.AD_LANGUAGE,
          OnboardingStepIds.AD_BANNERS,
          OnboardingStepIds.AD_VIDEOS,
          OnboardingStepIds.DETAILED_TARGETING,
          OnboardingStepIds.AD_BUDGET,
          OnboardingStepIds.FACEBOOK_PAGE,
          OnboardingStepIds.LEAD_FORM,
        ].includes(currentStepId);

        if (moveToNextStepOnSuccess) {
          moveToNextStep();
        }

        const populateCampaignDataFromBackground = [
          OnboardingStepIds.BUSINESS_DETAILS,
          OnboardingStepIds.AUDIENCE,
          OnboardingStepIds.PRODUCT_USPS,
          OnboardingStepIds.AD_LANGUAGE,
        ].includes(currentStepId);

        if (populateCampaignDataFromBackground) {
          populateCampaignDataUsingAi(response.data);
        }

        if (currentStepId === OnboardingStepIds.BUSINESS_DETAILS) {
          populateSpecialAdCategoriesUsingAi(response.data);
          populateSearchKeywordsSuggestionsUsingAi(response.data);
          if (campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX) {
            triggerCustomConversionActionCreation(response.data, false);
          }
        }
        if (currentStepId === OnboardingStepIds.LEAD_FORM) {
          if (campaignType === GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH) {
            // Google Search: create custom conversion action only in case of custom landing page
            if (
              !response.data?.google_ads_data?.lead_form_url ||
              response.data?.google_ads_data?.lead_form_url?.includes(
                'connectform.co/lead-forms',
              )
            ) {
              // do nothing
            } else {
              triggerCustomConversionActionCreation(response.data, true);
            }
          }
        }
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(error, 'onboarding.saveCampaignDetails');
      });
  };

  const renderStepDetails = () => {
    switch (selectedStepId) {
      case OnboardingStepIds.BUSINESS_DETAILS: {
        return (
          <BusinessDetailsComp
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            categories={businessCategoriesResponse?.data?.data ?? []}
            user={user}
          />
        );
      }
      case OnboardingStepIds.PRODUCT_USPS: {
        return (
          <ProductUspsComp
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            user={user}
          />
        );
      }
      case OnboardingStepIds.AUDIENCE: {
        return (
          <TargetAudienceComp
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            user={user}
          />
        );
      }
      case OnboardingStepIds.GOOGLE_KEYWORDS: {
        return (
          <GoogleKeywordsComp
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            user={user}
          />
        );
      }
      case OnboardingStepIds.AD_LANGUAGE: {
        return (
          <AdLanguageComp
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            user={user}
            onNextPress={moveToNextStep}
          />
        );
      }
      case OnboardingStepIds.AD_BANNERS: {
        return (
          <AdBannersComp
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            user={user}
            onNextPress={moveToNextStep}
          />
        );
      }
      case OnboardingStepIds.AD_VIDEOS: {
        return (
          <AdVideosComp
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            user={user}
            onNextPress={moveToNextStep}
          />
        );
      }
      case OnboardingStepIds.DETAILED_TARGETING: {
        return (
          <DetailedTargetingContainer
            campaignDetails={campaignDetails}
            user={user}
            saveCampaignDetails={saveCampaignDetails}
          />
        );
      }
      case OnboardingStepIds.AD_PREVIEW: {
        return (
          <AdPreviewContainer
            campaignDetails={campaignDetails}
            user={user}
            onCtaClick={() => {
              logEvent(EVENT_NAMES.ad_preview_next_clicked);
              moveToNextStep();
            }}
          />
        );
      }
      case OnboardingStepIds.AD_BUDGET: {
        return (
          <AdBudgetContainer
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            partnerConfig={partnerConfig}
          />
        );
      }
      case OnboardingStepIds.FACEBOOK_PAGE: {
        return (
          <FbPageContainer
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            user={user}
            partnerConfig={partnerConfig}
          />
        );
      }
      case OnboardingStepIds.LEAD_FORM: {
        return (
          <LeadFormComp
            campaignDetails={campaignDetails}
            saveCampaignDetails={saveCampaignDetails}
            user={user}
          />
        );
      }
      case OnboardingStepIds.REVIEW_AND_LAUNCH: {
        return (
          <ReviewAndLaunchCampaign
            campaignDetails={campaignDetails}
            user={user}
            saveCampaignDetails={saveCampaignDetails}
            campaignUpdateInProgress={createOrUpdateCampaignMutation.isLoading}
            partnerConfig={partnerConfig}
          />
        );
      }
      default:
        return null;
    }
  };

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 h-full w-full">
        <div className="flex items-center mt-4 ">
          <div className="p-4 cursor-pointer" onClick={onBackPressed}>
            <BackIcon />
          </div>
          <p className="text-black text-lg">
            {ONBOARDING_STEPS.find((item) => item.id === selectedStepId)?.label}
          </p>
          <div className="flex-1" />
        </div>
        <div className="my-2 flex items-start overflow-scroll px-4 no-scrollbar">
          {getFilteredOnboardingSteps().map((item, index) => {
            const selected = item.id === selectedStepId;
            return (
              <div
                key={index}
                className="cursor-pointer shrink-0 w-16 mr-3 last:mr-0"
                onClick={() => onOnboardingStepTabClick(item.id)}
                ref={(el) => (onboardingStepRefs.current[index] = el)}
              >
                <div
                  className={classNames(
                    'w-full transition-all duration-200 rounded-lg h-1',
                    {
                      'bg-primary h-1.5': selected,
                      'bg-gray-medium': !selected,
                    },
                  )}
                />
                <p
                  className={classNames('text-xxs mt-1', {
                    'text-primary': selected,
                    'text-gray-medium': !selected,
                  })}
                >
                  {item.label}
                </p>
              </div>
            );
          })}
        </div>
        <div className="bg-white flex flex-col flex-1 overflow-y-hidden h-full">
          {renderStepDetails()}
        </div>
        {campaignDetailsResponse.isLoading ||
        createOrUpdateCampaignMutation.isLoading ? (
          <FullScreenLoader />
        ) : null}
      </div>
    </MobileContainer>
  ) : null;
};

export default Onboarding;
