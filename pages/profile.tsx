import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

import { IGroweasyUser } from 'src/types';
import BackIcon from '@/images/common/back-arrow.svg';
import MobileContainer from '@/components/lib/MobileContainer';
import { showToastMessage } from 'src/modules/toast';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import Link from 'next/link';
import UserProfileImage from '@/components/lib/UserProfileImage';
import DeleteAccountBs from '@/components/profile/bottom_sheets/DeleteAccountBs';
import { useMutation, useQuery } from 'react-query';
import { deleteUserAccount, getAdCreditBalance } from 'src/actions/profile';
import { getCommonHeaders } from 'src/actions';
import {
  formatCurrencyAmount,
  logApiErrorAndShowToastMessage,
} from 'src/utils';
import { Currency } from 'src/types/campaigns';
import {
  FaFileInvoiceDollar,
  FaHistory,
  FaUserEdit,
  FaTrashAlt,
  FaSignOutAlt,
  FaCreditCard,
  FaMoneyBillWave,
} from 'react-icons/fa';

interface IProfileProps {
  user?: IGroweasyUser;
  onUserChange: (user: IGroweasyUser) => void;
}

const Profile = (props: IProfileProps) => {
  const { user, onUserChange } = props;

  const [showDeleteAccountBs, setShowDeleteAccountBs] = useState(false);

  const router = useRouter();

  const deleteAccountMutation = useMutation(deleteUserAccount);

  useEffect(() => {
    if (!user) {
      void router.push('/login');
    }
  }, [user, router]);

  useEffect(() => {
    // flutter webview will call these methods
    window.onLogout = () => {
      onUserChange(null);
      void router.push('/login');
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const adCreditsBalanceResponse = useQuery(
    'getAdCreditBalance',
    () => {
      return getAdCreditBalance({
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
      });
    },
    {
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'EditProfile.getAdCreditBalance');
      },
    },
  );

  const onBackPressed = () => {
    router.back();
  };

  const logoutUser = async () => {
    if (window.bridge) {
      window.bridge.postMessage('logout');
      return;
    }
    const { getAuth } = await import('firebase/auth');

    getAuth()
      .signOut()
      .then(() => {
        onUserChange(null);
        void router.push('/login');
      })
      .catch((error: Error) => {
        showToastMessage(error.message, 'error');
      });
  };

  const onLogoutClick = () => {
    logEvent(EVENT_NAMES.logout_clicked);
    void logoutUser();
  };

  const onDeleteAccountConfirmationClick = () => {
    deleteAccountMutation
      .mutateAsync({
        queryParams: router.query as Record<string, string>,
        headers: getCommonHeaders(user),
      })
      .then(() => {
        logEvent(EVENT_NAMES.account_deleted);
        showToastMessage('Account deletion success', 'success');
        void logoutUser();
      })
      .catch((error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'Profile.onDeleteAccountConfirmationClick',
        );
      });
  };

  const adCreditsbalance = adCreditsBalanceResponse?.data?.data;
  const displayBalances = Object.entries(adCreditsbalance || {})
    .filter(
      ([key, value]) =>
        key !== 'created_at' &&
        key !== 'updated_at' &&
        typeof value === 'number' &&
        value > 0,
    )
    .map(
      ([key, value]) =>
        `${formatCurrencyAmount(
          value as number,
          key.toUpperCase() as Currency,
        )}`,
    )
    .join(', ');

  return user ? (
    <MobileContainer>
      <div className="flex flex-col flex-1 w-full h-full bg-gray-lightest">
        <div className="flex items-center px-4 py-3 bg-white shadow-sm">
          <div
            className="p-2 cursor-pointer hover:bg-primary-light rounded-full transition-all duration-200"
            onClick={onBackPressed}
          >
            <BackIcon className="w-5 h-5 text-primary" />
          </div>
          <p className="text-primary text-lg font-semibold ml-2">My Profile</p>
        </div>

        <div className="flex-1 overflow-y-scroll no-scrollbar">
          <div className="mt-6 flex flex-col items-center px-5">
            <UserProfileImage user={user} width={90} height={90} />
            <p className="mt-3 text-xl font-semibold text-gray-darkest">
              {user.displayName}
            </p>
            <p className="text-sm text-gray-medium">{user.email}</p>
          </div>

          {displayBalances && (
            <div className="mt-6 mx-5 p-4 bg-white rounded-xl shadow">
              <p className="text-xs text-gray-medium font-medium">
                Ad Credits Balance
              </p>
              <p className="text-lg mt-1 font-semibold text-primary">
                {displayBalances}
              </p>
            </div>
          )}

          <div className="mt-6 mx-5 flex flex-col bg-white rounded-xl shadow overflow-hidden">
            {[
              {
                href: '/billing-info',
                label: 'Billing Info',
                icon: <FaCreditCard className="text-primary w-4 md:w-6" />,
              },
              {
                href: '/invoices',
                label: 'Invoices',
                icon: (
                  <FaFileInvoiceDollar className="text-primary w-4 md:w-6" />
                ),
              },
              {
                href: '/payment-history',
                label: 'Payment History',
                icon: <FaHistory className="text-primary w-4 md:w-6" />,
              },
              {
                href: '/ad-credit-transactions',
                label: 'Ad Credit Transactions',
                icon: <FaMoneyBillWave className="text-primary w-4 md:w-6" />,
              },
              {
                href: '/edit-profile?source=profile',
                label: 'Edit Profile',
                icon: <FaUserEdit className="text-primary w-4 md:w-6" />,
              },
            ].map((item, index) => (
              <Link
                href={item.href}
                key={item.href}
                className={`flex items-center p-4 text-sm text-gray-darkest hover:bg-primary-light transition-all duration-200 ${
                  index !== 0 ? 'border-t border-gray-light' : ''
                }`}
              >
                <span className="mr-3 text-lg">{item.icon}</span>
                {item.label}
              </Link>
            ))}
            <div
              className="flex items-center p-4 text-sm text-red cursor-pointer hover:bg-red/10 transition-all duration-200 border-t border-gray-light"
              onClick={() => setShowDeleteAccountBs(true)}
            >
              <span className="mr-3 text-lg">
                <FaTrashAlt className=" w-3 md:w-5" />
              </span>
              Delete Account
            </div>
          </div>
        </div>

        <div className="px-5 pb-6 pt-4 mt-4">
          <button
            className="w-full flex items-center justify-center text-sm text-red-dark bg-red-light hover:bg-red-dark hover:text-white rounded-lg p-3 transition-all duration-300 shadow active:scale-95"
            onClick={onLogoutClick}
          >
            <FaSignOutAlt className="mr-2" />
            Logout
          </button>
        </div>
        {showDeleteAccountBs ? (
          <DeleteAccountBs
            loading={deleteAccountMutation.isLoading}
            onConfirmClick={onDeleteAccountConfirmationClick}
            onClose={() => setShowDeleteAccountBs(false)}
          />
        ) : null}
      </div>
    </MobileContainer>
  ) : null;
};

export default Profile;
