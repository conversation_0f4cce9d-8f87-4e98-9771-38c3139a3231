import { useEffect, useState, Fragment } from 'react';
import { NextPageContext } from 'next';
import { User } from 'firebase/auth';
import Head from 'next/head';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import '../styles/globals.css';
import { GrowEasyPartners, IGroweasyUser, IPartnerConfig } from 'src/types';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import Script from 'next/script';
import { getMetaTags, getPartnerConfig, getPartnerFromHost } from 'src/utils';
import {
  BANNERBOT_GTAG_ID,
  CONNECTFORM_GTAG_ID,
  FB_PIXEL_ID,
  GROWEASY_APP_ID,
} from 'src/constants';
import { Jost, Poppins, Bricolage_Grotesque } from 'next/font/google';

const QueryClientProvider = dynamic(
  () => import('src/utils/QueryClientProvider'),
  {
    ssr: true,
  },
);

const jost = Jost({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
  variable: '--font-family-jost',
});

const poppins = Poppins({
  subsets: ['latin'],
  weight: ['100', '200', '300', '400', '500', '600', '700', '800', '900'],
  display: 'swap',
  variable: '--font-family-poppins',
});

const bricolage_grotesque = Bricolage_Grotesque({
  subsets: ['latin'],
  weight: ['700'],
  display: 'swap',
  variable: '--font-family-bricolage_grotesque',
});

const Toaster = dynamic(() => import('../src/modules/toast'));

const AUTH_ENABLED_PATHS = [
  '/dashboard',
  '/campaign-details',
  '/campaign-details/google',
  '/onboarding',
  '/profile',
  '/admin/dashboard',
  '/admin/campaign-details',
  '/admin/invoices',
  '/admin/invoice-details',
  '/payment-history',
  '/billing-info',
  '/invoices',
  '/invoice-details',
  '/payment',
  '/edit-profile',
  '/waba-onboarding',
  '/campaign-analytics',
  '/ad-credit-transactions',
];

function MyApp({ Component, pageProps, partner }) {
  const [user, setUser] = useState<IGroweasyUser | null>(null);
  const [partnerConfig] = useState<IPartnerConfig | null>(
    getPartnerConfig(partner as GrowEasyPartners),
  );

  const router = useRouter();

  // no need to show initialisation to non auth routes, it affects SEO
  const [initDone, setInitDone] = useState(
    !AUTH_ENABLED_PATHS.includes(router.pathname),
  );

  const setUserToSentry = (userProps = user) => {
    if (userProps?.uid && window?.sentry) {
      window.sentry.setUserToSentry({
        email: userProps.email,
        id: userProps.uid,
      });
    }
  };

  const getTokenAndSetUser = (user: User) => {
    user
      // forceRefresh = true -> refresh token and then return it
      .getIdTokenResult(true)
      .then((idTokenResult) => {
        const profile = idTokenResult.claims?.profile as {
          email: string;
          name: string;
        };
        const userProps: IGroweasyUser = {
          displayName: user.displayName ?? profile?.name,
          email: user.email ?? profile?.email,
          photoUrl: user.photoURL ?? '',
          uid: user.uid,
          authToken: idTokenResult.token,
          mobile: user.phoneNumber ?? '',
        };
        setUser(userProps);
        setUserToSentry(userProps);
        setInitDone(true);

        // schedule auto refresh
        const expirationTime = idTokenResult.expirationTime;

        // Calculate the time to set the next refresh
        const currentTime = Date.now();
        const expirationTimeMillis = new Date(expirationTime).getTime();
        const timeUntilRefresh =
          expirationTimeMillis - currentTime - 5 * 60 * 1000; // 5 minutes before expiry
        //console.log(`Token will be refreshed in ${timeUntilRefresh / 1000} secs`);

        // Set the timeout to refresh the token
        setTimeout(() => {
          getTokenAndSetUser(user);
        }, timeUntilRefresh);
      })
      .catch((error) => {
        console.error(error);
      });
  };

  useEffect(() => {
    // Lazy load Sentry and initialize it
    import('../src/modules/sentry')
      .then((sentry) => {
        window.sentry = sentry.default;
        window.sentry.initializeSentry();
        setUserToSentry();
      })
      .catch((error) => console.error(error));
  }, []);

  useEffect(() => {
    const initFirebaseAuth = async () => {
      const { initFirebase } = await import('src/modules/firebase');
      initFirebase();

      if (window.bridge) {
        return;
      }

      const { getAuth } = await import('firebase/auth');

      getAuth().onAuthStateChanged(function (user) {
        if (user) {
          getTokenAndSetUser(user);
        } else {
          setInitDone(true);
        }
      });
    };
    void initFirebaseAuth();
  }, []);

  useEffect(() => {
    window.platform = 'web'; // will be overridden in flutter app

    // flutter webview will call these methods
    window.onInitViaBridge = (user?: IGroweasyUser) => {
      if (user && user.authToken && user.email && user.uid) {
        setUser(user);
        setUserToSentry(user);
      }
      setInitDone(true);
    };
  }, []);

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      // should be same as /public/service-worker-vX.js, also update CACHE_NAME version in service worker file
      navigator.serviceWorker
        .register('/service-worker-v2.js')
        .then((registration) => {
          console.info('Service worker registered:', registration);
        })
        .catch((error) => {
          console.info('Service worker registration failed:', error);
        });
      /*navigator
          .serviceWorker
          .getRegistration()
          .then((serviceWorker) => {
              console.log(serviceWorker);
              if (serviceWorker) {
                  serviceWorker.unregister();  
          }})
          .catch((error) => {
              console.error("There was an error: ", error);
          });*/
    }
  }, []);

  return (
    <Fragment>
      <Head>
        <meta charSet="UTF-8" />
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
        />
        <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="theme-color" content="#294744" />
        <meta property="og:type" content="website" />
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link
          href="https://fonts.googleapis.com/css2?family=Inter:wght@100..900&display=swap"
          rel="stylesheet"
        />
        {partnerConfig ? (
          <>
            <title>{partnerConfig.meta?.title}</title>
            <meta
              name="description"
              content={partnerConfig.meta?.description}
            />
            <meta property="og:image" content={partnerConfig.meta?.ogImage} />
            <link
              rel="shortcut icon"
              type="image/x-icon"
              href={partnerConfig.meta?.favIcon}
            />
            <meta property="og:title" content={partnerConfig.name} />
          </>
        ) : (
          getMetaTags('/')
        )}
        {/* for meta tag overriding, we are using "key" attributes but for it to work, 
          all tags have to be at same level (hierarchy)
          <>Tags</> and <><>Tags</></> are not at same level
         */}
        {partnerConfig ? (
          <meta name="robots" key="robots" content="noindex, nofollow" />
        ) : (
          <meta name="robots" key="robots" content="index, follow" />
        )}
      </Head>
      <Toaster />
      {initDone ? (
        [
          ...AUTH_ENABLED_PATHS,
          '/login',
          '/order-social-media-videos-ads/order',
          '/campaign-onboarding-connect-facebook-pages',
          '/marketing-ai-tools/lead-cost-calculator',
          '/marketing-ai-tools/ai-ad-copywriter',
        ].includes(router.pathname) ? (
          <QueryClientProvider>
            {/* 
              To remove duplicacy of Below element, it can be made a separate component, but not as inline function  
              Inline function like `const MountedComp = () => { return <div .../> }` will cause re-mounting of pages when user state changes
            */}
            <div
              className={`${jost.variable} ${poppins.variable} ${bricolage_grotesque.variable}`}
            >
              <Component
                {...pageProps}
                user={user}
                onUserChange={(user: IGroweasyUser) => setUser(user)}
                partnerConfig={partnerConfig}
              />
            </div>
          </QueryClientProvider>
        ) : (
          <div
            className={`${jost.variable} ${poppins.variable} ${bricolage_grotesque.variable}`}
          >
            <Component
              {...pageProps}
              user={user}
              onUserChange={(user: IGroweasyUser) => setUser(user)}
              partnerConfig={partnerConfig}
            />
          </div>
        )
      ) : (
        <div className="flex flex-col items-center justify-center h-screen">
          <SpinnerLoader />
          <p className="mt-3">Initialising...</p>
        </div>
      )}
      {[
        ...AUTH_ENABLED_PATHS,
        '/order-social-media-videos-ads/order',
        '/digital-marketing-master-class',
        '/campaign-onboarding-connect-facebook-pages',
      ].includes(router.pathname) ? (
        <>
          <Script src="https://checkout.razorpay.com/v1/checkout.js" defer />
          <Script
            id="fb-sdk-init"
            dangerouslySetInnerHTML={{
              __html: `
              window.fbAsyncInit = function() {
                FB.init({
                  appId: '${GROWEASY_APP_ID}',
                  cookie: true,
                  xfbml: true,
                  version: 'v20.0'
                });
              };
            `,
            }}
          />
          <Script
            async
            defer
            crossOrigin="anonymous"
            src="https://connect.facebook.net/en_US/sdk.js"
          ></Script>
        </>
      ) : null}

      <Script
        id="fb-pixel"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
          !function(f,b,e,v,n,t,s)
          {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
          n.callMethod.apply(n,arguments):n.queue.push(arguments)};
          if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
          n.queue=[];t=b.createElement(e);t.async=!0;
          t.src=v;s=b.getElementsByTagName(e)[0];
          s.parentNode.insertBefore(t,s)}(window, document,'script',
          'https://connect.facebook.net/en_US/fbevents.js');
          fbq('init', '${FB_PIXEL_ID}');
          fbq('track', 'PageView');
        `,
        }}
      />
      {/* <Script
        id="sw-boradcast-listener"
        strategy="beforeInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            if (BroadcastChannel) {
              const broadcast = new BroadcastChannel('sw-update-channel');
              broadcast.onmessage = (event) => {
                if (event.data && event.data.eventName) {
                  const eventName = event.data.eventName;
                  const eventPayload = event.data;
                  // todo
                }
              };
            }
          `,
        }}
      /> */}
      <Script
        src={`https://www.googletagmanager.com/gtag/js?id=${BANNERBOT_GTAG_ID}`}
        strategy="afterInteractive"
      />
      <Script
        id="gtag"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());

            gtag('config', '${BANNERBOT_GTAG_ID}');
            gtag('config', '${CONNECTFORM_GTAG_ID}');
          `,
        }}
      />
    </Fragment>
  );
}

// https://nextjs.org/docs/pages/api-reference/functions/get-initial-props

MyApp.getInitialProps = async (context: {
  ctx: NextPageContext;
}): Promise<{
  host?: string;
  partner?: GrowEasyPartners;
  headers?: Record<string, string>;
  // eslint-disable-next-line @typescript-eslint/require-await
}> => {
  const { ctx } = context;

  // groweasy.ai leads.bannerbot.xyz
  // priority to x-forwarded-host since Cloudflare is throwing 403 on nginx reverse proxy when using actual host header
  const host =
    (ctx?.req?.headers['x-forwarded-host'] as string) ?? ctx?.req?.headers.host;
  const partner = getPartnerFromHost(host);

  return {
    host,
    partner,
  };
};

export default MyApp;
