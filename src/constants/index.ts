import { IUserProfile } from 'src/types';
import { Currency } from 'src/types/campaigns';
import { IS_PROD } from 'src/utils';

export const LOCAL_STORAGE_KEYS = {};

export const DASHBOARD_TABS = [
  {
    id: 'campaigns',
    label: 'Campaigns',
    iconWidth: 18,
    iconHeight: 18,
    iconUrl: '/images/dashboard/rocket-empty.png',
    selectedIconUrl: '/images/dashboard/rocket-filled.png',
  },
  {
    id: 'leads',
    label: 'Leads',
    iconWidth: 23,
    iconHeight: 18,
    iconUrl: '/images/dashboard/user-search-empty.png',
    selectedIconUrl: '/images/dashboard/user-search-filled.png',
  },
];

export enum OnboardingStepIds {
  BUSINESS_DETAILS = 'business-details',
  AUDIENCE = 'audience',
  GOOGLE_KEYWORDS = 'google-keywords',
  PRODUCT_USPS = 'product-usps',
  AD_LANGUAGE = 'ad-language',
  AD_BANNERS = 'ad-banners',
  AD_VIDEOS = 'ad-videos',
  AD_COPIES = 'ad-copies',
  AD_PREVIEW = 'ad-preview',
  FACEBOOK_PAGE = 'facebook-page',
  AD_BUDGET = 'ad-budget',
  LEAD_FORM = 'lead-form',
  REVIEW_AND_LAUNCH = 'review-and-launch',
  DETAILED_TARGETING = 'detailed-targeting',
}

export const ONBOARDING_STEPS = [
  {
    label: 'Business Details',
    id: OnboardingStepIds.BUSINESS_DETAILS,
  },
  {
    label: 'Target Audience',
    id: OnboardingStepIds.AUDIENCE,
  },
  {
    label: 'Product USPs',
    id: OnboardingStepIds.PRODUCT_USPS,
  },
  {
    label: 'Ad Language',
    id: OnboardingStepIds.AD_LANGUAGE,
  },
  {
    label: 'Keywords',
    id: OnboardingStepIds.GOOGLE_KEYWORDS,
  },

  {
    label: 'Ad Banners',
    id: OnboardingStepIds.AD_BANNERS,
  },
  {
    label: 'Ad Videos',
    id: OnboardingStepIds.AD_VIDEOS,
  },
  /*{
    label: 'Ad Copies',
    id: OnboardingStepIds.AD_COPIES,
  },
  {
    label: 'Ad Preview',
    id: OnboardingStepIds.AD_PREVIEW,
  },
  {
    label: 'Detailed Targeting',
    id: OnboardingStepIds.DETAILED_TARGETING,
  },*/
  {
    label: 'Ad Budget',
    id: OnboardingStepIds.AD_BUDGET,
  },
  {
    label: 'Facebook Page',
    id: OnboardingStepIds.FACEBOOK_PAGE,
  },
  {
    label: 'Lead Form',
    id: OnboardingStepIds.LEAD_FORM,
  },
  {
    label: 'Review & Launch',
    id: OnboardingStepIds.REVIEW_AND_LAUNCH,
  },
];

export enum QueryParams {
  CAMPAIGN_ID = 'campaign_id',
  AD_FORMAT = 'ad_format',
  LEADGEN_FORM_ID = 'leadgen_form_id',
  LIMIT = 'limit',
  START_AFTER = 'start_after',
  INVOICE_ID = 'invoice_id',
  ORDER_ID = 'order_id',
  STATUS = 'status',
  SOURCE = 'source',
  AD_ACCOUNT_ID = 'ad_account_id',
  ASSET_RESOURCE_NAMES = 'asset_resource_names',
  LAST_CURSOR_ID = 'last_cursor_id',
  PLATFORM = 'platform',
  TYPE = 'type',
  VERSION = 'version',
  BREAKDOWNS = 'breakdowns',
  SORT = 'sort',
  TOKEN = 'token',
  META_ID = 'meta_id',
  META_AD_IDS = 'meta_ad_ids',
  TEMPLATE_ID = 'template_id',
  FLOW_TYPE = 'flow_type',
  TIME_INCREMENT = 'time_increment',
  DATE_PRESET = 'date_preset',
  TIME_RANGE = 'time_range',
}

export const TARGETING_DEFAULT_MIN_AGE = 21;
export const TARGETING_DEFAULT_MAX_AGE = 65;
export const TARGETING_DEFAULT_GENDERS = [1, 2];

export const AD_FORMATS = [
  {
    format: 'MOBILE_FEED_STANDARD',
    label: 'Fecebook Feed',
  },
  {
    format: 'FACEBOOK_STORY_MOBILE',
    label: 'Facebook Story',
  },
  {
    format: 'INSTAGRAM_STANDARD',
    label: 'Instagram',
  },
];

export const CAMPAIGN_DETAILS_TABS: Array<{
  id: 'campaigns' | 'leads' | 'conversions';
  label: string;
}> = [
  {
    id: 'campaigns',
    label: 'Info',
  },
  {
    id: 'leads',
    label: 'Leads',
  },
  {
    id: 'conversions',
    label: 'Conversions',
  },
];

export const RAZORPAY_KEY_ID = IS_PROD
  ? 'rzp_live_gHyM5yLiqAKZC5'
  : 'rzp_test_eeEFoCcDL585gb';

export const FB_PIXEL_ID = '1344024836297864'; // groweasy-dataset
export const SALES_CAMPAIGN_FOR_CLIENTS_DATASET_ID = '637140262296819'; // newer version of Meta pixel
export const BANNERBOT_GTAG_ID = 'AW-11509365676';
export const CONNECTFORM_GTAG_ID = 'AW-11545814763';
export const GROWEASY_APP_ID = '729690972144381';
export const GROWEASY_WABA_ONBOARDING_CONFIG_ID = '1679824239088264';
export const GROWEASY_INTERAKT_SOLUTION_ID = '1029150822664030';
export const FB_LOGIN_TO_RUN_ADS_USING_USERS_PAGE_CONFIG_ID =
  '1490236605709034'; //'2283900718659968'; // SUAT is throwing permission error so using UAT
export const GROWEASY_DEFAULT_PAGE_ID = '498520620001493'; //'144441162089269';
export const AD_GLOBAL_AI_DEFAULT_PAGE_ID = '657706557424538';

export const MIN_DAILY_BUDGET = {
  [Currency.INR]: 1000, // Rs 1000
  [Currency.USD]: 100, // $100
  [Currency.IDR]: 50000, // Indonesian Rupiah
  [Currency.PHP]: 300, // Philippine Peso
  [Currency.THB]: 200, // Thai Baht
  [Currency.VND]: 250000, // Vietnamese Dong
  [Currency.MYR]: 30, // Malaysian Ringgit
};

// write in init/dashboard, read everywhere
// do not use for critical functionality since populating of this object is not guaranteed
export const GLOBALS: {
  userProfile?: IUserProfile;
} = {};

export const STRIPE_PUBLISHABLE_KEY = IS_PROD
  ? 'pk_live_51JDbx9SEry4Z0mFK5DYrc5wuQN5ZQEkEyzRrR7TnH6knCxluaaP4umx1avG5KOqOndB7Gq7zdXKZqJwaoMuvLoGn000ZsknAHp'
  : 'pk_test_51JDbx9SEry4Z0mFKOdNtOs0CApEtfx7RJf9bPFl0P8B9RUsZ7AU9ds9jp5L3QAQDfQ6IigRsHwWt2M0Muw1cmI4B003aEq6cig';

export const DIAL_CODE_TO_CURRENCY_MAP: Record<string, Currency> = {
  '+91': Currency.INR,
  '+1': Currency.USD,
  '+62': Currency.IDR, // Indonesia
  '+63': Currency.PHP, // Philippines
  '+66': Currency.THB, // Thailand
  '+84': Currency.VND, // Vietnam
  '+60': Currency.MYR, // Malaysia
  // add more as needed
};

export const CURRENCY_TO_LOCALE: Record<Currency, string> = {
  [Currency.INR]: 'en-IN',
  [Currency.USD]: 'en-US',
  [Currency.IDR]: 'id-ID',
  [Currency.PHP]: 'en-PH',
  [Currency.THB]: 'th-TH',
  [Currency.VND]: 'vi-VN',
  [Currency.MYR]: 'ms-MY',
};

export const VIDEO_FILE_SIZE_LIMIT_IN_KB = 30720; // 30MB
export const IMAGE_FILE_SIZE_LIMIT_IN_KB = 5120; // 5MB
