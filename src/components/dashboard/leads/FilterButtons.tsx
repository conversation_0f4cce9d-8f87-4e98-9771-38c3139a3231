import { useState } from 'react';
import DownArrow from '@/images/common/down-arrow.svg';
import CrossIcon from '@/images/common/cross.svg';
import { ILeadsStatus, LeadStatusNameMapping } from 'src/types/leads_crm';
import { ICampaign } from 'src/types/campaigns';

interface IFilterButtonsProps {
  campaigns: ICampaign[];
  onFiltersChange: (filters: ILeadFilters) => void;
}

export interface ILeadFilters {
  status?: ILeadsStatus;
  dateRange?: {
    start: string;
    end: string;
  };
  quality?: string;
  campaign?: string;
}

interface IFilterChip {
  key: string;
  label: string;
  onRemove: () => void;
}

const FilterButtons = (props: IFilterButtonsProps) => {
  const { campaigns, onFiltersChange } = props;

  const [activeFilters, setActiveFilters] = useState<ILeadFilters>({});
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);

  const statusOptions = [
    {
      value: ILeadsStatus.GOOD_LEAD_FOLLOW_UP,
      label: LeadStatusNameMapping[ILeadsStatus.GOOD_LEAD_FOLLOW_UP],
    },
    {
      value: ILeadsStatus.DID_NOT_CONNECT_OR_BUSY,
      label: LeadStatusNameMapping[ILeadsStatus.DID_NOT_CONNECT_OR_BUSY],
    },
    {
      value: ILeadsStatus.BAD_LEAD,
      label: LeadStatusNameMapping[ILeadsStatus.BAD_LEAD],
    },
    {
      value: ILeadsStatus.SALE_DONE,
      label: LeadStatusNameMapping[ILeadsStatus.SALE_DONE],
    },
  ];

  const qualityOptions = [
    { value: 'hot', label: 'Hot' },
    { value: 'interested', label: 'Interested' },
    { value: 'warm', label: 'Warm' },
    { value: 'cold', label: 'Cold' },
  ];

  const dateOptions = [
    { value: 'today', label: 'Today' },
    { value: 'yesterday', label: 'Yesterday' },
    { value: 'last_7_days', label: 'Last 7 days' },
    { value: 'last_30_days', label: 'Last 30 days' },
    { value: 'custom', label: 'Custom range' },
  ];

  const handleFilterSelect = (filterType: string, value: string) => {
    const newFilters = { ...activeFilters };

    if (filterType === 'status') {
      newFilters.status = value as ILeadsStatus;
    } else if (filterType === 'quality') {
      newFilters.quality = value;
    } else if (filterType === 'campaign') {
      newFilters.campaign = value;
    } else if (filterType === 'date') {
      newFilters.dateRange = { start: value, end: value };
    }

    setActiveFilters(newFilters);
    onFiltersChange(newFilters);
    setOpenDropdown(null);
  };

  const removeFilter = (filterType: string) => {
    const newFilters = { ...activeFilters };

    if (filterType === 'status') {
      delete newFilters.status;
    } else if (filterType === 'quality') {
      delete newFilters.quality;
    } else if (filterType === 'campaign') {
      delete newFilters.campaign;
    } else if (filterType === 'date') {
      delete newFilters.dateRange;
    }

    setActiveFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const FilterButton = ({
    title,
    filterKey,
    options,
  }: {
    title: string;
    filterKey: string;
    options: { value: string; label: string }[];
  }) => (
    <div className="relative">
      <button
        type="button"
        className="flex items-center px-3 sm:px-4 py-2 border border-gray-300 rounded-lg text-sm bg-white shadow"
        onClick={() =>
          setOpenDropdown(openDropdown === filterKey ? null : filterKey)
        }
      >
        <span>{title}</span>
        <DownArrow className="ml-2 w-3 h-3 text-gray-500" />
      </button>

      {openDropdown === filterKey && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-10 min-w-48 max-w-56 overflow-scroll pr-2">
          {options.map((option) => (
            <button
              type="button"
              key={option.value}
              className="block w-full text-left px-3 py-2 text-sm hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg"
              onClick={() => handleFilterSelect(filterKey, option.value)}
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
    </div>
  );

  const getActiveFilterChips = (): IFilterChip[] => {
    const chips: IFilterChip[] = [];

    if (activeFilters.status) {
      const statusLabel = statusOptions.find(
        (opt) => opt.value === activeFilters.status,
      )?.label;
      chips.push({
        key: 'status',
        label: `Status: ${statusLabel}`,
        onRemove: () => removeFilter('status'),
      });
    }

    if (activeFilters.dateRange) {
      const dateLabel = dateOptions.find(
        (opt) => opt.value === activeFilters.dateRange?.start,
      )?.label;
      chips.push({
        key: 'date',
        label: `Date: ${dateLabel}`,
        onRemove: () => removeFilter('date'),
      });
    }

    if (activeFilters.quality) {
      const qualityLabel = qualityOptions.find(
        (opt) => opt.value === activeFilters.quality,
      )?.label;
      chips.push({
        key: 'quality',
        label: `Quality: ${qualityLabel}`,
        onRemove: () => removeFilter('quality'),
      });
    }

    if (activeFilters.campaign) {
      const campaignLabel =
        campaigns.find((c) => c.id === activeFilters.campaign)?.friendly_name ||
        'Unknown';
      chips.push({
        key: 'campaign',
        label: `Campaign: ${campaignLabel}`,
        onRemove: () => removeFilter('campaign'),
      });
    }

    return chips;
  };

  const campaignOptions = campaigns.map((campaign) => ({
    value: campaign.id,
    label: campaign.friendly_name || campaign.name || 'Unnamed Campaign',
  }));

  return (
    <div className="mb-4">
      {/* Filter Buttons */}
      <div className="flex gap-2 flex-wrap">
        <FilterButton
          title="Status"
          filterKey="status"
          options={statusOptions}
        />
        <FilterButton title="Date" filterKey="date" options={dateOptions} />
        <FilterButton
          title="Quality"
          filterKey="quality"
          options={qualityOptions}
        />
        <FilterButton
          title="Campaign"
          filterKey="campaign"
          options={campaignOptions}
        />
      </div>

      {/* Active Filter Chips */}
      {getActiveFilterChips().length > 0 && (
        <div className="flex gap-2 flex-wrap mt-3">
          {getActiveFilterChips().map((chip) => (
            <div
              key={chip.key}
              className="flex items-center px-2 py-1.5 sm:px-4 bg-teal-700/15 border-primary2/40 border rounded-full text-xs"
            >
              <span>{chip.label}</span>
              <button
                type="button"
                className="ml-3 text-gray-500 hover:text-gray-700"
                onClick={chip.onRemove}
              >
                <CrossIcon className="size-2" />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Click outside to close dropdown */}
      {openDropdown && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setOpenDropdown(null)}
        />
      )}
    </div>
  );
};

export default FilterButtons;
