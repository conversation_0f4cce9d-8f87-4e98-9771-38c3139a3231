import CampaignLeadsListItem from '@/components/campaign_details/CampaignLeadsListItem';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { IPartnerConfig } from 'src/types';
import { ICampaign } from 'src/types/campaigns';
import { ICtwaLead, IMetaLead } from 'src/types/leads';
import { ILeadsCrmDetails } from 'src/types/leads_crm';
import { getParseLead } from 'src/utils';
import NoDataFound from '../../lib/NoDataFound';
import FilterButtons, { ILeadFilters } from './FilterButtons';
import { useState } from 'react';

interface ILeadsListProps {
  data: Array<(IMetaLead | ICtwaLead) & { crm_details?: ILeadsCrmDetails }>;
  onNextPress: () => void;
  noMoreLeadsAvailable: boolean;
  loading: boolean;
  campaigns: ICampaign[];
  onEditLeadsCrmClick: (details: Partial<ILeadsCrmDetails>) => void;
  partnerConfig?: IPartnerConfig;
}

const LeadsList = (props: ILeadsListProps) => {
  const {
    data,
    onNextPress,
    noMoreLeadsAvailable,
    loading,
    campaigns,
    onEditLeadsCrmClick,
    partnerConfig,
  } = props;

  const [filters, setFilters] = useState<ILeadFilters>({});

  const handleFiltersChange = (newFilters: ILeadFilters) => {
    setFilters(newFilters);
  };

  // Filter the data based on active filters
  const filteredData = data.filter((item) => {
    if (filters.status && item.crm_details?.status !== filters.status) {
      return false;
    }

    // Campaign filter
    if (filters.campaign) {
      const campaignDetails = campaigns?.find((campaignItem) => {
        if ('form_id' in item) {
          return campaignItem.meta_leadgen_form_id === item.form_id;
        } else {
          return campaignItem.id === item.campaign_id;
        }
      });
      if (campaignDetails?.id !== filters.campaign) {
        return false;
      }
    }

    return true;
  });
  console.log({ filteredData });

  if (!data.length) {
    return (
      <NoDataFound
        illustration={{
          url: '/images/dashboard/leads-illustration.svg',
          width: 200,
          height: 200,
        }}
        title="No Leads Found!"
        imageClassName="rounded-xl"
      />
    );
  }

  return (
    <div className="flex flex-col w-full">
      {/* Filter Buttons */}
      <FilterButtons
        campaigns={campaigns}
        onFiltersChange={handleFiltersChange}
      />

      <div className="flex flex-col">
        {filteredData.map((item, index) => {
          const campaignDetails = campaigns?.find((campaignItem) => {
            if ('form_id' in item) {
              return campaignItem.meta_leadgen_form_id === item.form_id;
            } else {
              return campaignItem.id === item.campaign_id;
            }
          });
          const campaignName =
            campaignDetails?.friendly_name ??
            campaignDetails?.details?.business_details?.business_category ??
            'NA';
          return (
            <div key={index} className="mt-5 first:mt-0">
              <p className="text-sm text-gray-dark font-medium">
                {campaignName}
              </p>
              <CampaignLeadsListItem
                data={getParseLead(
                  item,
                  campaignDetails?.details?.leadgen_form?.questions ?? [],
                )}
                className="!mt-2"
                campaignName={campaignDetails?.name}
                leadsCrmDetails={item.crm_details}
                onEditLeadsCrmClick={(leadCrmDetails) => {
                  leadCrmDetails.campaign_id = campaignDetails.id;
                  onEditLeadsCrmClick(leadCrmDetails);
                }}
                partnerConfig={partnerConfig}
              />
            </div>
          );
        })}
      </div>
      {noMoreLeadsAvailable ? null : (
        <div className="my-3">
          <p
            className="text-sm text-hyperlink text-center cursor-pointer"
            onClick={onNextPress}
          >
            Load More
          </p>
        </div>
      )}
      {loading ? (
        <div className="flex justify-center mt-2">
          <SpinnerLoader size={24} borderWidth={2} />
        </div>
      ) : null}
    </div>
  );
};

export default LeadsList;
