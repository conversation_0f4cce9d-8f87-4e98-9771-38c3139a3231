import { GrowEasyCampaignStatus, ICampaign } from 'src/types/campaigns';
import TickIcon from '@/images/common/tick.svg';
import PauseIcon from '@/images/common/pause.svg';
import EditIcon from '@/images/common/edit.svg';

interface ICampaignStatusCompProps {
  campaignDetails: ICampaign;
}

const CampaignStatusComp = (props: ICampaignStatusCompProps) => {
  const { campaignDetails } = props;

  const renderStatus = () => {
    const status = campaignDetails.status;
    if (status === GrowEasyCampaignStatus.ACTIVE) {
      const campaignEndDate = new Date(
        campaignDetails.details?.budget_and_scheduling?.end_time,
      );
      const currentDate = new Date();
      if (campaignEndDate > currentDate) {
        return (
          <div className="flex items-center bg-red-light py-1 px-2 rounded-md">
            <div className="h-3 w-3 bg-red rounded-full mr-2" />
            <p className="text-xs text-red">Live</p>
          </div>
        );
      } else {
        return (
          <div className="flex items-center bg-green-light py-1 px-2 rounded-md">
            <div className=" w-3 ">
              <TickIcon className="text-green-dark" />
            </div>
            <p className="text-xs text-green-dark">Complete</p>
          </div>
        );
      }
    } else if (status === GrowEasyCampaignStatus.PAUSED) {
      return (
        <div className="flex items-center bg-yellow-light py-1 px-2 rounded-md">
          <PauseIcon className="text-yellow-dark mr-2" />
          <p className="text-xs text-yellow-dark">Paused</p>
        </div>
      );
    } else if (status === GrowEasyCampaignStatus.DRAFT) {
      return (
        <div className="flex items-center bg-gray-light py-1 px-2 rounded-md">
          <EditIcon className="text-gray-dark mr-2" />
          <p className="text-xs text-gray-dark">Draft</p>
        </div>
      );
    } else {
      return (
        <div className="flex items-center bg-gray-light py-1 px-2 rounded-md">
          <p className="text-xs text-gray-dark">{status}</p>
        </div>
      );
    }
  };

  return renderStatus();
};

export default CampaignStatusComp;
