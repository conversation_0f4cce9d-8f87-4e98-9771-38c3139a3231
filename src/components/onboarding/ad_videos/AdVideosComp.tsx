import { useRouter } from 'next/router';
import { useState, useEffect, useRef, ChangeEventHandler } from 'react';
import { useMutation, useQuery } from 'react-query';
import Button from '@/components/lib/Button';
import SpinnerLoader from '@/components/lib/SpinnerLoader';
import { getCommonHeaders } from 'src/actions';
import {
  getAdVideosDetails,
  uploadAdVideo,
  // uploadAiGeneratedAdVideo,
} from 'src/actions/onboarding';
import { AdPlatforms, IGroweasyUser } from 'src/types';
import {
  ICampaign,
  IAdVideo,
  GROWEASY_CAMPAIGN_TYPE,
} from 'src/types/campaigns';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import FullScreenLoader from '@/components/lib/FullScreenLoader';
import {
  OnboardingStepIds,
  QueryParams,
  VIDEO_FILE_SIZE_LIMIT_IN_KB,
} from 'src/constants';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import UploadedAdVideos from './UploadedAdVideos';
import Image from 'next/image';
import AiVideoBottomSheet from './AiVideoBottomSheet';
import { showToastMessage } from 'src/modules/toast';
import DragAndDrop from '@/components/lib/DragAndDrop';

interface IAdVideosCompProps {
  campaignDetails: ICampaign;
  saveCampaignDetails: (
    data: ICampaign,
    currentStepId: OnboardingStepIds,
  ) => void;
  user: IGroweasyUser;
  onNextPress: () => void;
}

const AdVideosComp = (props: IAdVideosCompProps) => {
  const { campaignDetails, saveCampaignDetails, user, onNextPress } = props;

  const [uploadAdVideosInProgress, setUploadAdVideosInProgress] =
    useState(false);
  const [existingAdVideos, setExistingAdVideos] = useState<IAdVideo[]>([]);
  const [selectedVideoFiles, setSelectedVideoFiles] = useState<File[]>([]);
  const [isAiVideoBottomSheetOpen, setIsAiVideoBottomSheetOpen] =
    useState(false);
  const [isDragging, setIsDragging] = useState(false);

  const uploadVideoInputRef = useRef<HTMLInputElement>(null);

  const router = useRouter();
  const adPlatform =
    campaignDetails?.platform ??
    router.query[QueryParams.PLATFORM] ??
    AdPlatforms.META;

  const adVideosResponse = useQuery(
    ['getAdVideosDetails', campaignDetails.id],
    () =>
      getAdVideosDetails({
        queryParams: {
          ...router.query,
          ids: existingAdVideos.map((item) => item.id)?.join(','),
        } as Record<string, string>,
        headers: getCommonHeaders(user),
      }),
    {
      enabled: !!existingAdVideos?.filter((item) => !!item.id)?.length,
      retry: 0,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(error, 'AdVideosComp.adVideosResponse');
      },
    },
  );

  const uploadAdVideoMutation = useMutation(uploadAdVideo);

  // const uploadAiGeneratedAdVideoMutation = useMutation(
  //   uploadAiGeneratedAdVideo,
  // );

  // const generateAiVideoAdMutation = useMutation(generateBannerBasedVideoAd);

  useEffect(() => {
    setExistingAdVideos(campaignDetails?.details?.ad_videos ?? []);
  }, [campaignDetails]);

  const onUploadIconPress = () => {
    uploadVideoInputRef.current?.click();
  };

  const onUploadImageInputChange: ChangeEventHandler<HTMLInputElement> = (
    e,
  ) => {
    const fileList = e.target?.files;
    const files = [];

    if (fileList.length === 0) return;

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      // check for file type
      if (!file.type.startsWith('video')) {
        showToastMessage(
          'Invalid file type! Please upload a valid file.',
          'error',
        );
      } else {
        // check for file size
        const fileSizeInKb = file.size / 1024;
        if (fileSizeInKb > VIDEO_FILE_SIZE_LIMIT_IN_KB) {
          showToastMessage(
            `File "${file.name}" exceeds the maximum size of ${
              VIDEO_FILE_SIZE_LIMIT_IN_KB / 1024
            } MB.`,
            'error',
          );
        } else {
          files.push(file);
        }
      }
    }

    setSelectedVideoFiles(files);
  };

  const uploadAdVideoAndReturnDetails = async (): Promise<IAdVideo[]> => {
    const promises = [];
    const adVideos: IAdVideo[] = [];
    setUploadAdVideosInProgress(true);
    const queryParams = {
      ...(router.query as Record<string, string>),
      [QueryParams.AD_ACCOUNT_ID]:
        campaignDetails?.details?.config?.ad_account_id ?? '',
    };
    if (selectedVideoFiles?.length) {
      selectedVideoFiles.forEach((file) => {
        promises.push(
          uploadAdVideoMutation.mutateAsync({
            headers: getCommonHeaders(user),
            queryParams,
            blob: file,
            fileName: file.name,
            campaignType: campaignDetails?.type,
          }),
        );
      });
    }
    try {
      const uploadResponses = (await Promise.all(promises)) as Array<{
        data: IAdVideo;
      }>;
      uploadResponses.forEach((response) => {
        adVideos.push(response.data);
      });
    } catch (error) {
      logApiErrorAndShowToastMessage(
        error as Error,
        'AdVideosComp.uploadAdVideoAndReturnDetails',
      );
    } finally {
      setUploadAdVideosInProgress(false);
    }
    return adVideos;
  };

  const saveUploadedVideosDetails = (adVideos: IAdVideo[]) => {
    if (!adVideos.length) {
      return;
    }
    const updatedCampaignDetails: ICampaign = {
      ...campaignDetails,
      details: {
        ...campaignDetails?.details,
        ad_videos: adVideos,
      },
    };
    saveCampaignDetails(updatedCampaignDetails, OnboardingStepIds.AD_BANNERS);
  };

  const onSaveDetailsClick = async () => {
    logEvent(EVENT_NAMES.ad_videos_save_clicked);
    const adVideos = await uploadAdVideoAndReturnDetails();
    saveUploadedVideosDetails(adVideos);
  };

  const onSkipThisStepClick = () => {
    logEvent(EVENT_NAMES.ad_videos_skip_clicked);
    onNextPress();
  };

  const handleAiGeneratedVideoClick = () => {
    setIsAiVideoBottomSheetOpen(true);
    logEvent(EVENT_NAMES.use_ai_generated_video_clicked);
  };

  const videoFileSizeExceeded =
    selectedVideoFiles.length !== 0 &&
    Math.ceil(selectedVideoFiles[0]?.size / 1024) > VIDEO_FILE_SIZE_LIMIT_IN_KB;

  const enableBannerBasedVideoOption =
    adPlatform === AdPlatforms.META ||
    campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX;

  return (
    <div className="p-4 flex flex-col flex-1 h-full">
      {existingAdVideos.length ? (
        adVideosResponse.isLoading ? (
          <div className="flex flex-col items-center mt-3">
            <SpinnerLoader />
          </div>
        ) : (
          <UploadedAdVideos
            videos={Object.values(adVideosResponse.data?.data ?? {})}
            onReset={() => {
              logEvent(EVENT_NAMES.ad_videos_reset_clicked);
              setExistingAdVideos([]);
            }}
            onNextPress={onNextPress}
            uploadedVideos={campaignDetails?.details?.ad_videos}
          />
        )
      ) : (
        <div className="flex flex-col flex-1 h-full">
          <div className="flex flex-col flex-1 h-full">
            <div className="flex-1 overflow-y-scroll no-scrollbar mt-2">
              <p className="text-lg tracking-tight font-semibold">
                Do you have a video?
              </p>
              <p className="text-xs md:text-sm mt-2 text-primary p-2.5 bg-primary/5 rounded-lg border-primary border">
                Note : campaign with at least 1 video performs 2x better!
              </p>

              <p className="text-xs text-gray-dark mt-3">
                A combination of image and video ads is recommended for optimal
                performance.
              </p>
              <p className="text-xs text-gray-dark mt-2">
                Video ads often outperform image ads, sometimes cutting lead
                costs by up to 50%.
              </p>
              <DragAndDrop
                acceptedTypes={['video/*']}
                handleUpload={onUploadImageInputChange}
                setIsDragging={setIsDragging}
                maxFileSizeInKB={VIDEO_FILE_SIZE_LIMIT_IN_KB}
              >
                <div
                  className="rounded-xl mt-5 flex flex-col items-center justify-center p-6 md:p-8 border-dashed border-2 hover:border-primary data-[isDragging=true]:border-primary transition-all cursor-pointer"
                  data-isDragging={isDragging}
                  onClick={onUploadIconPress}
                >
                  <input
                    title="video"
                    type="file"
                    ref={uploadVideoInputRef}
                    onChange={onUploadImageInputChange}
                    className="hidden"
                    accept="video/*"
                  />
                  <div className="flex">
                    <Image
                      src="/images/common/upload-icon.png"
                      width={52}
                      height={52}
                      alt="upload icon"
                    />
                  </div>
                  {selectedVideoFiles.length === 0 ? (
                    <p className="text-xs text-gray-medium font-medium mt-4">
                      Click or Drag to Upload a video (Max size:{' '}
                      {VIDEO_FILE_SIZE_LIMIT_IN_KB / 1024} MB)
                    </p>
                  ) : null}
                  <div className="mt-4">
                    {selectedVideoFiles.map((file, index) => {
                      return (
                        <div key={index}>
                          <p className="text-xs text-gray-medium font-medium">{`${
                            file.name
                          }, ${Math.round(file.size / (1024 * 1024))} MB`}</p>
                        </div>
                      );
                    })}
                  </div>
                  {videoFileSizeExceeded ? (
                    <p className="text-xs mt-3 text-red text-center">
                      Please select a video with a size of less than{' '}
                      {VIDEO_FILE_SIZE_LIMIT_IN_KB / 1024} MB.
                    </p>
                  ) : null}
                </div>
              </DragAndDrop>
              {enableBannerBasedVideoOption ? (
                <div className="flex flex-col items-center gap-4 mt-2">
                  <p className="text-center mt-3 text-gray-medium">OR</p>
                  <button
                    type="button"
                    className="relative bg-gradient-to-r from-primary to-primary via-green-900/65 bg-clip-text text-transparent text-center font-bold cursor-pointer mr-1 text-lg px-3 md:px-12 py-3 md:py-4 border border-primary rounded-lg hover:scale-105 transition-all duration-300 md:w-fit w-full mx-1"
                    onClick={() => void handleAiGeneratedVideoClick()}
                  >
                    <p className="absolute -top-2 right-1 text-[10px] leading-none mb-2 text-primary bg-teal-50 rounded-full px-3 py-1 border-green-900 border font-medium tracking-tight">
                      Recommended
                    </p>
                    Use AI Generated Video ✨
                  </button>
                </div>
              ) : null}
            </div>
            <Button
              onClick={() => void onSaveDetailsClick()}
              disabled={!selectedVideoFiles.length || videoFileSizeExceeded}
              className="mt-3"
            >
              <p>Upload</p>
            </Button>
            <p
              className="text-center text-sm text-hyperlink mt-2 cursor-pointer"
              onClick={onSkipThisStepClick}
            >
              Skip this Step
            </p>
          </div>
        </div>
      )}
      {uploadAdVideosInProgress ? <FullScreenLoader /> : null}
      {isAiVideoBottomSheetOpen && (
        <AiVideoBottomSheet
          onClose={() => setIsAiVideoBottomSheetOpen(false)}
          onVideoGenerated={saveUploadedVideosDetails}
          user={user}
          campaignDetails={campaignDetails}
          queryParams={router.query as Record<string, string>}
        />
      )}
    </div>
  );
};

export default AdVideosComp;
