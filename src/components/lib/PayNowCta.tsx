import { useEffect, useState } from 'react';
import { useMutation, useQuery } from 'react-query';
import { loadStripe } from '@stripe/stripe-js';
import { getCommonHeaders } from 'src/actions';
import { getOrderDetails } from 'src/actions/dashboard';
import { verifyPayment } from 'src/actions/onboarding';
import {
  GLOBALS,
  RAZORPAY_KEY_ID,
  STRIPE_PUBLISHABLE_KEY,
} from 'src/constants';
import { EVENT_NAMES } from 'src/constants/events';
import { logEvent } from 'src/modules/firebase';
import { showToastMessage } from 'src/modules/toast';
import { IGroweasyUser, IPartnerConfig } from 'src/types';
import { Currency, ICampaign } from 'src/types/campaigns';
import {
  IOrderDetails,
  IRazorpayCheckoutHandlerResponse,
  IRazorpayCheckoutInstance,
  IRazorpayCheckoutOptions,
} from 'src/types/payments_invoices';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import Button from './Button';
import { Elements } from '@stripe/react-stripe-js';
import StripeCardPaymentBs from './StripeCardPaymentBs';
import XenditPaymentBs from './XenditPaymentBs';

interface ICompletePaymentCta {
  orderId?: string;
  orderDetails?: IOrderDetails;
  user: IGroweasyUser;
  onSuccess: () => void;
  automaticallyInitiatePayment?: boolean;
  campaignDetails: ICampaign;
  partnerConfig?: IPartnerConfig;
}

const stripePromise = loadStripe(STRIPE_PUBLISHABLE_KEY);

const CompletePaymentCta = (props: ICompletePaymentCta) => {
  const {
    orderId,
    orderDetails,
    user,
    onSuccess,
    automaticallyInitiatePayment,
    campaignDetails,
    partnerConfig,
  } = props;

  const [showStripePaymentBs, setShowStripePaymentBs] = useState(false);
  const [showXenditPaymentBs, setShowXenditPaymentBs] = useState(false);

  const orderDetailsResponse = useQuery(
    ['getOrderDetails', orderId],
    () =>
      getOrderDetails({
        orderId,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!orderId && !orderDetails,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'CompletePaymentCta.getOrderDetails',
        );
      },
    },
  );

  const verifyPaymentMutation = useMutation(verifyPayment);

  const onPaymentResponseReceived = (
    response:
      | IRazorpayCheckoutHandlerResponse
      | {
          stripe_payment_intent_id: string;
        },
  ) => {
    void verifyPaymentMutation.mutateAsync(
      {
        headers: getCommonHeaders(user),
        queryParams: {},
        data: response,
      },
      {
        onError: (error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'CompletePaymentCta.onPaymentResponseReceived',
          );
        },
        onSuccess: () => {
          showToastMessage('Payment Success', 'success');
          logEvent(EVENT_NAMES.payment_success);
          setShowStripePaymentBs(false);
          onSuccess();
        },
      },
    );
  };

  const orderData: IOrderDetails =
    orderDetails ?? orderDetailsResponse?.data?.data;

  const onPayNowClick = () => {
    if (!orderData) {
      return;
    }
    if (!GLOBALS.userProfile?.is_affiliate_marketing) {
      logEvent(EVENT_NAMES.payment_initiated);
    }
    if (orderData.currency === Currency.USD) {
      // International Payment on Razorpay enabled now
      //setShowStripePaymentBs(true);
      //return;
    } else if (
      [
        Currency.IDR,
        Currency.MYR,
        Currency.PHP,
        Currency.THB,
        Currency.VND,
      ].includes(orderData.currency)
    ) {
      setShowXenditPaymentBs(true);
      return;
    }
    const prefill: {
      email: string;
      contact?: string;
    } = {
      email: user.email,
    };
    if (campaignDetails?.details?.business_details?.mobile) {
      prefill.contact = campaignDetails?.details?.business_details?.mobile;
    }
    const options: IRazorpayCheckoutOptions = {
      key: partnerConfig?.razorpayKeyId ?? RAZORPAY_KEY_ID,
      amount: orderData.amount,
      currency: orderData.currency,
      name: partnerConfig?.name ?? 'GrowEasy',
      description: 'Transaction to launch lead generation campaign',
      image:
        partnerConfig?.meta?.ogImage ??
        'https://groweasy.ai/images/groweasy-logo-square.png',
      order_id: orderData.razorpay_order_id,
      prefill,
      theme: {
        color: '#294744',
      },
      handler: onPaymentResponseReceived,
    };
    if (!window?.Razorpay) {
      return;
    }
    const rzp = new window.Razorpay(options) as IRazorpayCheckoutInstance;
    rzp.on('payment.failed', function (response) {
      logEvent(EVENT_NAMES.payment_failed);
      showToastMessage(`Payment failed: ${response?.error?.reason}`, 'error');
    });
    rzp.open();
  };

  useEffect(() => {
    if (automaticallyInitiatePayment) {
      onPayNowClick();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [automaticallyInitiatePayment, orderData]);

  return (
    <div className="flex flex-col">
      <Elements stripe={stripePromise}>
        <Button
          className="mt-2"
          onClick={onPayNowClick}
          disabled={orderDetailsResponse.isLoading}
        >
          <p>Complete Payment</p>
        </Button>
        {showStripePaymentBs ? (
          <StripeCardPaymentBs
            onClose={() => setShowStripePaymentBs(false)}
            user={user}
            orderDetails={orderData}
            onSuccess={(response) => onPaymentResponseReceived(response)}
          />
        ) : null}
        {showXenditPaymentBs ? (
          <XenditPaymentBs
            onClose={() => setShowXenditPaymentBs(false)}
            user={user}
            orderDetails={orderData}
            partnerConfig={partnerConfig}
          />
        ) : null}
      </Elements>
    </div>
  );
};

export default CompletePaymentCta;
