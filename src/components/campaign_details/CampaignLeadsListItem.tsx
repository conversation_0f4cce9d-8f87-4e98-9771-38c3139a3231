import { IParsedLead } from 'src/types/leads';
import EmailIcon from '@/images/common/email.svg';
import PhoneIcon from '@/images/common/phone.svg';
import Image from 'next/image';
import { getFormattedTimeString, shareText } from 'src/utils';
import Accordion from '../lib/Accordion';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from 'src/constants/events';
import EditIcon from '@/images/common/edit.svg';
import { ILeadsCrmDetails, LeadStatusNameMapping } from 'src/types/leads_crm';
import { GrowEasyPartners, IPartnerConfig } from 'src/types';

interface ICampaignLeadsListItemProps {
  data: IParsedLead;
  className?: string;
  campaignName: string;
  leadsCrmDetails?: ILeadsCrmDetails;
  onEditLeadsCrmClick?: (leadsCrmDetails: Partial<ILeadsCrmDetails>) => void;
  partnerConfig?: IPartnerConfig;
}

const leadStatusColorMap = {
  CONTACTED: '#C9EBFF', // Light Blue
  IRRELEVANT: '#FFE5E5', // Light Red
  DID_NOT_CONNECT: '#FFDAB9', // Peach
  FOLLOW_UP: '#D9FFDB', // Light Green
  INTERESTED: '#FFECB3', // Light Yellow
  SALE_DONE: '#E0E0E0', // Light Gray
  NOT_INTERESTED: '#FFD1D1', // Light Pink
  YET_TO_CONTACT: '#F3F3F3', // Off-White
  // new status
  DID_NOT_CONNECT_OR_BUSY: '#FFDAB9', // Peach
  GOOD_LEAD_FOLLOW_UP: '#D9FFDB', // Light Green
  BAD_LEAD: '#FFE5E5', // Light Red
};

const leadNoteColorMap = {
  CONTACTED: '#004080', // Dark Blue
  IRRELEVANT: '#800000', // Dark Red
  DID_NOT_CONNECT: '#FF8C00', // Dark Orange
  FOLLOW_UP: '#006400', // Dark Green
  INTERESTED: '#8B4513', // Saddle Brown
  SALE_DONE: '#404040', // Dark Gray
  NOT_INTERESTED: '#800080', // Dark Purple
  YET_TO_CONTACT: '#333333', // Dark Charcoal
  // new status
  DID_NOT_CONNECT_OR_BUSY: '#FF8C00', // Dark Orange
  GOOD_LEAD_FOLLOW_UP: '#006400', // Dark Green
  BAD_LEAD: '#800000', // Dark Red
};

const CampaignLeadsListItem = (props: ICampaignLeadsListItemProps) => {
  const {
    data,
    className = '',
    campaignName,
    leadsCrmDetails,
    onEditLeadsCrmClick,
    partnerConfig,
  } = props;

  const name =
    data.field_data?.find((fieldItem) => fieldItem.type === 'FULL_NAME')
      ?.values?.[0] ?? '';
  const email =
    data.field_data?.find((fieldItem) => fieldItem.type === 'EMAIL')
      ?.values?.[0] ?? '';
  const phone =
    data.field_data?.find((fieldItem) => fieldItem.type === 'PHONE')
      ?.values?.[0] ?? '';
  // "2023-12-08T13:52:26+0000" or 1702153807
  const createdTime =
    typeof data.created_time === 'string'
      ? data.created_time
      : data.created_time * 1000;

  const onShareIconClicked = () => {
    logEvent(EVENT_NAMES.lead_share_icon_clicked);
    let textToBeShared = `${
      partnerConfig?.partner === GrowEasyPartners.NIVIDA ? '' : campaignName
    }

Name: ${name}
Mobile: ${phone}
Email: ${email}
`;
    data.field_data
      ?.filter((fieldItem) => fieldItem.type === 'CUSTOM')
      .map((item, index) => {
        const value = item.values?.[0];
        textToBeShared += `
${index + 1}. ${item.label}: ${value}
`;
      });
    shareText(textToBeShared);
  };

  const statusColorCode = leadStatusColorMap[leadsCrmDetails?.status];
  const noteColorCode = leadNoteColorMap[leadsCrmDetails?.status];

  return (
    <div className={`mt-3 p-3 bg-white rounded-lg shadow ${className}`}>
      <div className="flex items-center flex-1">
        <div className="w-full">
          <div className="flex items-center">
            <p className="text-sm font-medium mr-2">
              {data.navlink ? (
                <a
                  href={data.navlink}
                  target="_blank"
                  className="text-hyperlink"
                >
                  {name}
                </a>
              ) : (
                <span>{name}</span>
              )}
            </p>
            {leadsCrmDetails?.status ? (
              <p
                className="mr-2 text-xs px-1 py-0.5 rounded"
                style={{
                  background: statusColorCode,
                }}
              >
                {LeadStatusNameMapping[leadsCrmDetails?.status] ??
                  leadsCrmDetails?.status?.replaceAll('_', ' ')}
              </p>
            ) : null}
            <div className="flex-1" />
            {onEditLeadsCrmClick ? (
              <div
                className="cursor-pointer"
                onClick={() =>
                  onEditLeadsCrmClick(
                    leadsCrmDetails ?? { leadgen_id: data.id },
                  )
                }
              >
                <EditIcon className="text-gray-dark" />
              </div>
            ) : null}
          </div>
          {email ? (
            <div className="flex items-center mt-2">
              <div className="text-gray-dark mr-1 h-3.5 w-3.5">
                <EmailIcon />
              </div>
              <p className="text-xs text-gray-dark">{email}</p>
            </div>
          ) : null}
          <div className="flex items-center mt-1">
            <div className="text-gray-dark mr-1 h-3.5 w-3.5 ">
              <PhoneIcon />
            </div>
            <p className="text-xs text-gray-dark">{phone}</p>
          </div>
        </div>
      </div>
      <div className="flex items-center mt-3">
        <a
          className="mr-2"
          href={`tel:${phone}`}
          onClick={() => logEvent(EVENT_NAMES.lead_call_icon_clicked)}
        >
          <Image
            src="/images/common/call-icon.png"
            width="28"
            height="28"
            alt=""
          />
        </a>
        <a
          className="mr-2"
          href={`https://wa.me/${phone}`}
          target="_blank"
          onClick={() => logEvent(EVENT_NAMES.lead_whatsapp_icon_clicked)}
        >
          <Image
            src="/images/common/whatsapp-icon.png"
            width="28"
            height="28"
            alt=""
          />
        </a>
        <div
          className="rounded-full border border-gray-light flex items-center justify-center mr-2"
          style={{ height: 28, width: 28 }}
        >
          <a href="#" onClick={onShareIconClicked}>
            <Image
              src="/images/common/share-icon.png"
              width="16"
              height="16"
              alt=""
            />
          </a>
        </div>
      </div>
      <Accordion title="More Details" className="mt-3">
        <div>
          {data.field_data
            ?.filter((fieldItem) => fieldItem.type === 'CUSTOM')
            .map((item, index) => {
              const value = item.values?.[0];
              return (
                <div className="mt-2" key={index}>
                  <p className="text-xs text-gray-dark">
                    {index + 1}. {item.label}
                  </p>
                  <p className="text-xs font-medium mt-1">{value}</p>
                </div>
              );
            })}
          <p className="mt-3 text-xs text-gray-dark">
            Received on: {getFormattedTimeString(new Date(createdTime))}
          </p>
          {leadsCrmDetails?.note ? (
            <div>
              <div className="my-3 w-full h-px bg-gray-light" />
              <p className="mt-3 text-xs text-gray-dark font-medium underline">
                Note
              </p>
              <p className="mt-2 text-xs" style={{ color: noteColorCode }}>
                {leadsCrmDetails.note}
              </p>
            </div>
          ) : null}
        </div>
      </Accordion>
    </div>
  );
};

export default CampaignLeadsListItem;
