import {
  GROWEASY_CAMPAIGN_TYPE,
  GrowEasyCampaignStatus,
  ICampaign,
  ICampaignInsightDetails,
  IGoogleMediaAssetDetails,
  IGoogleSearchKeywordsInsights,
  IGoogleSearchLocationsInsights,
} from 'src/types/campaigns';
import GoogleAdPreviewsComp from './GoogleAdPreviewsComp';
import { AdPlatforms, IGroweasyUser, IPartnerConfig } from 'src/types';
import { useMutation, useQuery } from 'react-query';
import { getOrderDetails } from 'src/actions/dashboard';
import { getCommonHeaders } from 'src/actions';
import { logApiErrorAndShowToastMessage } from 'src/utils';
import GoogleSelectedGeoLocations from './GoogleSelectedGeoLocations';
import CampaignProgressBarComp from '@/components/dashboard/campaigns/CampaignProgressBarComp';
import CampaignInsightsComp from '../CampaignInsightsComp';
import LeadsQualifyingQuestionsAccordion from '../LeadsQualifyingQuestionsAccordion';
import CampaignInfoTabFooter from '../CampaignInfoTabFooter';
import { useState } from 'react';
import ConfirmDuplicateCampaignBs from '../bottom_sheets/ConfirmDuplicateCampaignBs';
import ExtendCampaignBs from '../bottom_sheets/ExtendCampaignBs';
import LeadTagSetupAccordion from './LeadTagSetupAccordion';
import KeywordsAccordion from './KeywordsAccordion';
import KeywordsInsightsAccordion from './KeywordsInsightsAccordion';
import SalesLandingPageUrlComp from '../SalesLandingPageUrlComp';
import SalesTagSetupAccordion from './SalesTagSetupAccordion';
import CampaignInvoicesAccordion from '../CampaignInvoicesAccordion';
import ConfirmStopCampaignBs from '../bottom_sheets/StopCampaignBs';
import { logEvent } from 'src/modules/firebase';
import { EVENT_NAMES } from '@/constants/events';
import { useRouter } from 'next/router';
import { updateCampaignStatus } from 'src/actions/onboarding';
import LocationsInsightsAccordion from './LocationsInsightsAccordion';

interface IGoogleCampaignInfoTabProps {
  className?: string;
  campaignDetails: ICampaign;
  mediaAssetsDetails: IGoogleMediaAssetDetails[];
  user?: IGroweasyUser;
  campaignInsightsDetails: ICampaignInsightDetails | null;
  partnerConfig?: IPartnerConfig;
  onCampaignStatusUpdate: () => void;
  keywordsInsightsDetails: IGoogleSearchKeywordsInsights[];
  locationsInsightsDetails: IGoogleSearchLocationsInsights[];
}

const GoogleCampaignInfoTab = (props: IGoogleCampaignInfoTabProps) => {
  const {
    className = '',
    campaignDetails,
    mediaAssetsDetails,
    user,
    campaignInsightsDetails,
    partnerConfig,
    onCampaignStatusUpdate,
    keywordsInsightsDetails,
    locationsInsightsDetails,
  } = props;

  const googleAdsData = campaignDetails?.google_ads_data;

  const [showDuplicateCampaignBs, setShowDuplicateCampaignBs] = useState(false);
  const [showExtendCampaignBs, setShowExtendCampaignBs] = useState(false);
  const [showStopCampaignBs, setShowStopCampaignBs] = useState(false);

  const updateCampaignStatusMutation = useMutation(updateCampaignStatus);

  const router = useRouter();

  const makeApiCallToUpdateCampaignStatus = (
    status: GrowEasyCampaignStatus,
  ) => {
    void updateCampaignStatusMutation.mutateAsync(
      {
        headers: getCommonHeaders(user),
        queryParams: router.query as Record<string, string>,
        data: {
          id: campaignDetails.id,
          status,
        },
      },
      {
        onError: (error: Error) => {
          logApiErrorAndShowToastMessage(
            error,
            'GoogleCampaignInfoTab.updateCampaignStatus',
          );
        },
        onSuccess: () => {
          onCampaignStatusUpdate();
        },
      },
    );
  };

  const orderDetailsResponse = useQuery(
    ['getOrderDetails', campaignDetails?.order_id],
    () =>
      getOrderDetails({
        orderId: campaignDetails?.order_id,
        headers: getCommonHeaders(user),
      }),
    {
      retry: 0,
      enabled: !!campaignDetails?.order_id,
      onError: (error: Error) => {
        logApiErrorAndShowToastMessage(
          error,
          'GoogleCampaignInfoTab.orderDetailsResponse',
        );
      },
    },
  );

  const onStopCampaignClick = () => {
    logEvent(EVENT_NAMES.stop_campaign_clicked);
    makeApiCallToUpdateCampaignStatus(GrowEasyCampaignStatus.ARCHIVED);
    setShowStopCampaignBs(false);
  };

  const orderDetails = orderDetailsResponse?.data?.data ?? null;
  const connectformLeadForm =
    campaignDetails.google_ads_data?.lead_form_url?.includes(
      'connectform.co/lead-forms',
    );

  return (
    <div
      className={`flex flex-col flex-1 overflow-y-hidden h-full p-4 ${className}`}
    >
      <div className="flex flex-col flex-1 overflow-y-scroll no-scrollbar pb-1">
        <GoogleAdPreviewsComp
          googleAdsData={googleAdsData}
          mediaAssetsDetails={mediaAssetsDetails}
          type={campaignDetails?.type}
          campaignDetails={campaignDetails}
        />
        <div className="mt-4 p-4 bg-white rounded-lg shadow">
          <p className="text-sm font-medium">Locations</p>
          <GoogleSelectedGeoLocations
            geoLocations={googleAdsData?.geo_locations}
            className="mt-3"
          />
          <CampaignProgressBarComp
            campaignDetails={campaignDetails}
            className="mt-3"
            adPlatform={AdPlatforms.GOOGLE}
          />
        </div>
        {campaignInsightsDetails ? (
          <CampaignInsightsComp
            data={campaignInsightsDetails}
            className="mt-4"
            budgetAndScheduling={
              campaignDetails?.details?.budget_and_scheduling
            }
            campaignType={campaignDetails.type}
          />
        ) : null}
        {campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_SEARCH ? (
          <LeadsQualifyingQuestionsAccordion
            leadgenForm={campaignDetails?.details?.leadgen_form}
            googleAdsData={googleAdsData}
            campaignStatus={campaignDetails?.status}
            adPlatform={campaignDetails?.platform}
          />
        ) : campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX ? (
          <SalesLandingPageUrlComp
            url={campaignDetails?.details?.business_details?.website}
          />
        ) : null}
        {googleAdsData ? (
          <>
            {campaignDetails?.type === GROWEASY_CAMPAIGN_TYPE.GOOGLE_P_MAX ? (
              <SalesTagSetupAccordion googleAdsData={googleAdsData} />
            ) : connectformLeadForm ? null : (
              <LeadTagSetupAccordion googleAdsData={googleAdsData} />
            )}
          </>
        ) : null}
        {keywordsInsightsDetails?.length ? (
          <KeywordsInsightsAccordion
            keywordsInsightsDetails={keywordsInsightsDetails}
          />
        ) : (
          <KeywordsAccordion googleAdsData={googleAdsData} />
        )}
        {locationsInsightsDetails?.length ? (
          <LocationsInsightsAccordion
            locationsInsightsDetails={locationsInsightsDetails}
          />
        ) : null}
        {orderDetails?.status === 'paid' ? (
          <CampaignInvoicesAccordion
            campaignDetails={campaignDetails}
            user={user}
            className="mt-3"
          />
        ) : null}
      </div>
      <CampaignInfoTabFooter
        orderDetails={orderDetails}
        campaignDetails={campaignDetails}
        onExtendCampaignClick={() => setShowExtendCampaignBs(true)}
        onPaymentSuccess={() => {
          onCampaignStatusUpdate();
          void orderDetailsResponse.refetch();
        }}
        user={user}
        partnerConfig={partnerConfig}
        onDuplicateCampaignClick={() => setShowDuplicateCampaignBs(true)}
        onStopCampaignClick={() => setShowStopCampaignBs(true)}
      />
      {showDuplicateCampaignBs ? (
        <ConfirmDuplicateCampaignBs
          campaignDetails={campaignDetails}
          user={user}
          onClose={() => setShowDuplicateCampaignBs(false)}
        />
      ) : null}
      {showExtendCampaignBs ? (
        <ExtendCampaignBs
          campaignDetails={campaignDetails}
          user={user}
          onClose={() => setShowExtendCampaignBs(false)}
        />
      ) : null}
      {showStopCampaignBs ? (
        <ConfirmStopCampaignBs
          onStopCampaignClick={onStopCampaignClick}
          loading={updateCampaignStatusMutation.isLoading}
          onClose={() => setShowStopCampaignBs(false)}
        />
      ) : null}
    </div>
  );
};

export default GoogleCampaignInfoTab;
