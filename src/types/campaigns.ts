import { AdPlatforms, ISelfAdAccountConfigDetails } from '.';
import { IBannerImage } from './banner_templates';

export enum ConsumerType {
  B2B = 'B2B',
  B2C = 'B2C',
  All = 'All',
}

export interface IBusinessDetails {
  business_category: string;
  product_or_service_description: string;
  product_or_service_offers_or_usp?: string;
  consumer_type?: ConsumerType;
  ideal_customers?: string;
  website?: string;
  mobile?: string;
  country_code?: string;
  mobile_without_country_code?: string;
  business_name?: string;
  business_logo?: {
    square: {
      url: string;
      width: number;
      height: number;
    };
  };
}

export enum LocationType {
  CITY = 'city',

  // not being used currently, it needs lat & long
  CUSTOM_LOCATION = 'custom_location',
  NEIGHBORHOOD = 'neighborhood',
  REGION = 'region',
  COUNTRY = 'country',
  PLACE = 'place',
}

export interface ILocationDetails {
  distance_unit?: 'mile' | 'kilometer';
  key?: string;
  name?: string;
  region?: string;
  region_id?: number;
  radius?: number;
  country_name?: string;
  latitude?: number;
  longitude?: number;
  primary_city_id?: number;
  address_string?: string;
  country_code?: string; // search API returns this
  country?: string; // creation API expects this
  type?: LocationType;
  primary_city?: string;
}

export interface IGeoLocations {
  countries?: string[];
  cities?: ILocationDetails[];
  regions?: ILocationDetails[];
  neighborhoods?: ILocationDetails[];
  places?: ILocationDetails[];
  custom_locations?: ILocationDetails[];
  location_types: string[];
}

export type FlexibleTargetingItemType =
  | 'behaviors'
  | 'interests'
  | 'life_events'
  | 'education_statuses'
  | 'work_positions'
  | 'industries';

export interface IFlexibleTargetingItem {
  id: string;
  name: string;
  type?: FlexibleTargetingItemType;
  key?: string;
  description?: string;
  path?: string[];
}

export type IFlexibleSpecItem = Record<
  FlexibleTargetingItemType,
  IFlexibleTargetingItem[]
>;

export interface ITargeting {
  age_min: number;
  age_max: number;
  genders: number[];
  geo_locations: IGeoLocations;
  flexible_spec?: IFlexibleSpecItem[];
  // Reach people beyond your detailed targeting selections when it's likely to improve performance.
  targeting_optimization?: 'none' | 'expansion_all';
}

export interface ICurrencyBudget {
  exchange_rate?: number;
  lifetime_budget: number;
  daily_budget: number;
}

// In Razorpay order creation, multiply by 100 for currencies with 2 decimal places
export enum Currency {
  INR = 'INR', // 2 decimal (paise)
  USD = 'USD', // 2 decimal (cents)
  IDR = 'IDR', // 0 decimal (Rupiah has no subunits)
  PHP = 'PHP', // 2 decimal (centavo)
  THB = 'THB', // 2 decimal (satang)
  VND = 'VND', // 0 decimal (Dong has no subunits)
  MYR = 'MYR', // 2 decimal (sen)
}

export interface IBudgetAndScheduling {
  daily_budget: number; // in paise (currency depends on ad account, Groweasy here)
  start_time: string;
  // lifetime_budget = daily_budget * no_of_days
  lifetime_budget: number; // in paise
  end_time: null | string;
  platform_fee_percentage?: number;
  // for PG - Razorpay/Stripe, Meta will always accept in INR
  // if it is USD, read usd object and so on
  currency?: Currency;
  usd?: ICurrencyBudget; // amounts in USD cents
  idr?: ICurrencyBudget; // amounts in IDR
  php?: ICurrencyBudget; // amounts in PHP
  thb?: ICurrencyBudget; // amounts in THB
  vnd?: ICurrencyBudget; // amounts in VND
  myr?: ICurrencyBudget; // amounts in MYR
}

enum LeadsAdCtaType {
  APPLY_NOW = 'APPLY_NOW',
  DOWNLOAD = 'DOWNLOAD',
  GET_QUOTE = 'GET_QUOTE',
  LEARN_MORE = 'LEARN_MORE',
  SIGN_UP = 'SIGN_UP',
  SUBSCRIBE = 'SUBSCRIBE',
}

export interface IAdCopy {
  call_to_action_type: LeadsAdCtaType;
  description: string; // (25 chars)
  primary_text: string; // message / primary_text / body (125 chars)
  headline: string; // heading, title, headline, name ((40 chars))
}

export interface IGoogleAdCopy {
  description: string; // 90 characters,	At least 1 with 60 characters or less
  long_headline: string; // 90 characters
  headline: string; // 30 characters,	At least 1 with 15 characters or less
  short_headline: string; // 15 characters or less
  short_description: string; // 60 characters or less
}

export interface IAdBanner {
  image?: {
    hash: string;
    width: number;
    height: number;
    s3_url?: string;
  };
  banner_data?: {
    creative_title: string;
    call_out: string;
    call_to_action: string;
    creative_image_url: string;
    size: string;
    template_id: string;
  };
  hidden?: boolean; // won't be visible to user, for placement optimisation
}

export interface ILeadgenFormQuestion {
  type: 'FULL_NAME' | 'EMAIL' | 'PHONE' | 'CUSTOM';
  key: string;
  label?: string;
  options?: Array<{
    key: string;
    value: string;
  }>;
}

export interface ILeadgenForm {
  name: string;
  is_optimized_for_quality: boolean;
  // To filter out organic leads
  block_display_for_non_targeted_viewer: boolean;
  tracking_parameters?: Record<string, string>;
  questions: ILeadgenFormQuestion[];
  privacy_policy: {
    url: string;
    link_text: string;
  };
  follow_up_action_url: string;
  follow_up_action_text: string;
}

export interface ICampaignConfig {
  fb_page_id: string;
  page_post_id?: string;
  ad_account_id?: string;
  meta_sales_purchase_event_name?: string;
  google_custom_conversion_action_doc_id?: string;
  // enable conversion api sending events
  meta_capi_enabled?: boolean;
  self_ad_account_configs?: ISelfAdAccountConfigDetails;
}

export enum AdLanguage {
  ENGLISH = 'English',
  HINDI = 'Hindi',
  BENGALI = 'Bengali',
  FRENCH = 'French',
  GERMAN = 'German',
  GUJARATI = 'Gujarati',
  KANNADA = 'Kannada',
  MALAYALAM = 'Malayalam',
  MARATHI = 'Marathi',
  ODIA = 'Odia',
  PORTUGUESE = 'Portuguese',
  RUSSIAN = 'Russian',
  SPANISH = 'Spanish',
  TAMIL = 'Tamil',
  ARABIC = 'Arabic',
  TELUGU = 'Telugu',
}

export interface ICampaignDetails {
  business_details?: IBusinessDetails;
  targeting?: ITargeting;
  budget_and_scheduling?: IBudgetAndScheduling;
  ad_copies?: IAdCopy[];
  ad_banners?: IAdBanner[];
  leadgen_form?: ILeadgenForm;
  config?: ICampaignConfig;
  ad_videos?: IAdVideo[];
  ad_language?: AdLanguage;
  ai_assisted_product_usps?: string[];
}

export enum GrowEasyCampaignStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  ARCHIVED = 'ARCHIVED',
}

export enum MetaCampaignEffectiveStatus {
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  DELETED = 'DELETED',
  ARCHIVED = 'ARCHIVED',
  IN_PROCESS = 'IN_PROCESS',
  WITH_ISSUES = 'WITH_ISSUES',
}

// https://developers.facebook.com/docs/marketing-api/audiences/special-ad-category/
enum MetaSpecialAdCategory {
  HOUSING = 'HOUSING',
  CREDIT = 'CREDIT',
  EMPLOYMENT = 'EMPLOYMENT',
  ISSUES_ELECTIONS_POLITICS = 'ISSUES_ELECTIONS_POLITICS',
  NONE = 'NONE',
}

// https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-issues-info/
export interface IMetaCampaignIssueInfo {
  error_code: number;
  error_message: string;
  error_summary: string;
  error_type: string;

  // Indicate level of issue, could be ad set or campaign
  level: string;
}

export interface IFirestoreTimestamp {
  _seconds: number;
}

// https://developers.google.com/google-ads/api/performance-max/asset-requirements
export enum GoogleAssetFieldType {
  HEADLINE = 'HEADLINE',
  LONG_HEADLINE = 'LONG_HEADLINE',
  DESCRIPTION = 'DESCRIPTION',
  BUSINESS_NAME = 'BUSINESS_NAME',
  MARKETING_IMAGE = 'MARKETING_IMAGE',
  SQUARE_MARKETING_IMAGE = 'SQUARE_MARKETING_IMAGE',
  LOGO = 'LOGO', // 1:1
  PORTRAIT_MARKETING_IMAGE = 'PORTRAIT_MARKETING_IMAGE',
  YOUTUBE_VIDEO = 'YOUTUBE_VIDEO',
}

export interface IGoogleAdsData {
  media_assets?: {
    [field in
      | GoogleAssetFieldType.MARKETING_IMAGE
      | GoogleAssetFieldType.SQUARE_MARKETING_IMAGE
      | GoogleAssetFieldType.PORTRAIT_MARKETING_IMAGE
      | GoogleAssetFieldType.LOGO
      | GoogleAssetFieldType.YOUTUBE_VIDEO]: Array<{
      resource_name: string;
    }>;
  };
  ad_copies?: IGoogleAdCopy[];
  ad_videos?: Array<{
    video_url: string;
    youtube_video_id?: string;
  }>;
  text_assets?: {
    [field in
      | GoogleAssetFieldType.BUSINESS_NAME
      | GoogleAssetFieldType.HEADLINE
      | GoogleAssetFieldType.LONG_HEADLINE
      | GoogleAssetFieldType.DESCRIPTION]: Array<{
      text: string;
      resource_name: string;
    }>;
  };
  campaign_resource?: string;
  campaign_budget_resource?: string;
  conversion_action_resource?: string;
  lead_form_url?: string;
  geo_locations?: IGoogleLocationDetails[];
  search_keywords?: string[];
  asset_group_resource?: string;
  custom_conversion_goal_resource?: string;

  // format AW-CONVERSION_ID/CONVERSION_LABEL, e.g. AW-***********/1Rf3CN69vPsZEKz3i_Aq
  conversion_action_event_label?: string;

  lead_form_content?: {
    heading: string;
    subheading: string;
    description: string;
    value_props: string[];
    cta_title: string;
    cta_label: string;
  };

  search_keywords_suggestions?: {
    [category: string]: string[];
  };
}

// https://developers.facebook.com/docs/marketing-api/reference/ad-campaign-group/
export interface ICampaign {
  id?: string;
  name?: string; // for BE, Ads manager
  friendly_name?: string; // For customers to avoid confusion when having same categories
  created_at?: IFirestoreTimestamp;
  updated_at?: IFirestoreTimestamp;
  status?: GrowEasyCampaignStatus;

  // populate meta fields while interacting with Meta Marketing APIs
  meta_id?: string;
  details?: ICampaignDetails;
  uid?: string; // firebase uid
  meta_created_time?: string;
  special_ad_categories?: MetaSpecialAdCategory[];
  country_code?: string; // US/IN etc

  // https://developers.facebook.com/docs/marketing-api/adset/budget-limits/v18.0
  currency?: string;

  // Issues for this campaign that prevented it from delivering
  meta_issues_info?: IMetaCampaignIssueInfo[];
  meta_adset_id?: string;
  meta_ad_ids?: string[];
  meta_leadgen_form_id?: string;
  order_id?: string;
  type?: GROWEASY_CAMPAIGN_TYPE; // LEAD_FORM by default

  google_ads_data?: IGoogleAdsData;
  platform?: AdPlatforms;
}

export interface IUploadAdBannerResponse {
  images: {
    [name: string]: {
      hash: string;
      height: number;
      url: string;
      name: string;
      url_128: string;
      width: number;
      url_256: string;
      url_256_height: string;
      url_256_width: string;
    };
  };
  s3_url?: string;
}

export interface ICampaignInsightDetails {
  clicks: string;
  impressions: string;
  publisher_platform?: string;
  spend: string;
  reach: string;
  date_start: string;
  date_stop: string;
  account_id: string;
  cpc: string;
  ctr: string;
  leads?: number;
  conversions?: number; // google only
  // meta only
  // onsite_conversion.lead_grouped: All On-Facebook Leads
  // lead: All offsite leads plus all On-Facebook leads
  // offsite_conversion.fb_pixel_custom: Custom fb event
  // purchase: Standard Purchase event
  cost_per_action_type?: Array<{
    action_type: string;
    value: string;
  }>;
  // meta only: break_downs
  region?: string;
  body_asset?: {
    text: string;
  };
  media_asset?: {
    asset_type: 'image_asset' | 'video_asset';
    hash: string;
    // thumbnail url in case of video from BE, Meta might return actual video url
    thumbnail_url?: string;
    url: string;
  };
  age?: string;
  gender?: string;
  hourly_stats_aggregated_by_audience_time_zone?: string;
}

export enum GROWEASY_CAMPAIGN_TYPE {
  LEAD_FORM = 'LEAD_FORM',
  CTWA = 'CTWA',
  GOOGLE_P_MAX = 'GOOGLE_P_MAX',
  GOOGLE_SEARCH = 'GOOGLE_SEARCH',
  META_SALES = 'META_SALES',
  GOOGLE_CALL = 'GOOGLE_CALL',
}

export interface IAdVideoDetails {
  id: string;
  permalink_url: string;
  length: number;
  updated_time: string;
  thumbnails: {
    data: {
      id: string;
      height: number;
      scale: number;
      uri: string;
      width: number;
      is_preferred: boolean;
    }[];
  };
  format: {
    embed_html: string;
    filter: string;
    height: number;
    picture: string;
    width: number;
  }[];
}

export interface IAdVideo {
  id?: string; // meta id
  video_url: string;
  youtube_video_id?: string;
}

export enum IVideoFrameV2Type {
  engaging_question = 'engaging_question',
  product_or_service_introduction = 'product_or_service_introduction',
  content = 'content',
  conclusion = 'conclusion',
}

// interface IVideoFrameDataV2 {
//   image_keywords?: string[];
//   images?: IBannerImage[];
//   voiceover_text: string;
//   highlighted_text: string;
//   videos?: IPexelsVideoData[];
//   video_content_description_involving_people?: string;
//   type: IVideoFrameV2Type;
// }

// export interface IVideoDataV2 {
//   video_caption: string;
//   frames: IVideoFrameDataV2[];
// }

interface IFrameElementContainer {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface IVideoFrameTextProps {
  container: IFrameElementContainer;
  value: string;
  maxFontSize: number;
  fontColor: string;
}

export interface IVideoFrameMediaProps {
  borderRadius: number;
  url: string;
  container: IFrameElementContainer;
}

export interface IVideoFrameData {
  type: IVideoFrameV2Type;
  duration: number;
  textProps: IVideoFrameTextProps;
  mediaType: 'image' | 'video';
  mediaProps?: IVideoFrameMediaProps;
  images?: IBannerImage[];
  voiceover_text: string;
}

export interface IVideoTemplate {
  id: string;
  size: 'portrait' | 'landscape';
  width: number;
  height: number;
  base_video_url: string;
  video_caption: string;
  frames: IVideoFrameData[];
  voice?: {
    languageCode: 'en-IN' | 'en-US';
    name?: 'en-IN-Standard-C';
    ssmlGender: 1 | 2 | 3; // 1: MALE, 2: FEMALE, 3: NEUTRAL
  };
}

export interface IAiVideoAdTemplate {
  template: IVideoTemplate;
  audio_url: string;
}

export interface IBannerBasedVideoTemplate {
  id: string;
  size: 'portrait' | 'landscape';
  width: number;
  height: number;
  base_video_url: string;
  bg_music_url: string;
  video_caption: string;
  frames: Array<{
    duration: number;
    textPropsArr: IVideoFrameTextProps[];
    mediaPropsArr: IVideoFrameMediaProps[];
  }>;
}

interface IGoogleCampaignMetrics {
  clicks: string;
  costMicros: string;
  ctr: number;
  averageCpc: number;
  impressions: string;
  conversions: number;
  allConversions: number;
}
export interface IGoogleCampaignInsights {
  campaign: {
    resourceName: string;
    status: string;
    name: string;
  };
  metrics: IGoogleCampaignMetrics;
  segments: {
    device: 'MOBILE' | 'TABLET' | 'CONNECTED_TV';
  };
}

export interface IGoogleSearchKeywordsInsights {
  metrics: IGoogleCampaignMetrics;
  adGroupCriterion: {
    resourceName: string;
    keyword: {
      matchType: 'EXACT' | 'BROAD' | 'PHRASE';
      text: string;
    };
  };
  keywordView: {
    resourceName: string;
  };
}

export interface IGoogleSearchLocationsInsights {
  metrics: IGoogleCampaignMetrics;
  segments: {
    geoTargetCity: string;
    geoTargetCityCanonicalName?: string;
  };
  geographicView: {
    resourceName: string;
  };
}

export interface IGoogleLocationDetails {
  geoTargetConstant: {
    resourceName: string;
    id: string;
    name: string;
    countryCode: string;
    canonicalName: string;
  };
}

export interface IGoogleMediaAssetDetails {
  asset: {
    resourceName: string;
    type: 'YOUTUBE_VIDEO' | 'IMAGE';
    youtubeVideoAsset?: {
      youtubeVideoId: string;
      youtubeVideoTitle: string;
    };
    imageAsset?: {
      mimeType: string;
      fullSize: {
        url: string;
      };
      fileSize: string;
    };
    id: string;
    name: string;
  };
}

export interface IGoogleKeywordIdeas {
  keywordIdeaMetrics: {
    competition: string;
    monthlySearchVolumes: Array<{
      month: string;
      year: string;
      monthlySearches: string;
    }>;
    avgMonthlySearches: string | number;
    competitionIndex: string;
    lowTopOfPageBidMicros: string;
    highTopOfPageBidMicros: string;
  };
  text: string;
  closeVariants?: string[];
}

export interface ILanguageSuggestion {
  language: string;
  reason: string;
}

export interface ILanguageSuggestionsResponse {
  data: {
    language_suggestions: ILanguageSuggestion[];
  };
}
export interface IBannerElement {
  creative_title: string;
  call_out: string;
  call_to_action: string;
  creative_image_keywords: string[];
  imagery: string;
  focused_usp: string;
}

export interface IProductUspsAndBannerElementsResponse {
  data: {
    banners: IBannerElement[];
  };
}

export interface GenerateAiBannerResponseData {
  hash: string;
  width: number;
  height: number;
  s3_url: string;
}

export interface GenerateAiBannerResponse {
  data: GenerateAiBannerResponseData;
}
