import {
  IBusinessDetails,
  IFirestoreTimestamp,
  ILeadgenFormQuestion,
} from './campaigns';

export interface IMetaLead {
  created_time: string | number;
  id: string;
  ad_id: string;
  form_id: string;
  field_data: Array<{
    name: string;
    values: string[];
  }>;
}

export interface IParsedLead {
  id: string;
  created_time: string;
  field_data: Array<
    {
      name: string;
      values: string[];
    } & ILeadgenFormQuestion
  >;
  navlink?: string;
}

export interface ICtwaLead {
  created_time: number;
  // this will be used in chat link, to keep it smaller using meta_id instead of campaign_id
  id: string; // <wa_id>_<meta_id>
  meta_id: string;
  campaign_id: string;
  wa_id: string;
  field_data: Array<{
    name: string;
    label: string;
    values: string[];
  }>;
  client_uid: string;
  business_details: Partial<IBusinessDetails>;
}

export interface IGoogleLead {
  campaign_id: string;
  field_data: IParsedLead['field_data'];
  created_at: IFirestoreTimestamp;
  created_time: number; // in seconds (in sync with IMetaLead)
  uid: string;
  id: string;
}
